// simply take the csv of tee times and our facility mapping
// and dump the data into rabbit

// usage: cat kubtest-gn-api-inventory.json | node rabbit_stream_stdin.js gn-api-inventory

'use strict';

const Q = require('q');
const Split = require('split');

// passed around?
const locals = {};

const connectToRabbitMQStream = function () {

    const deferred = Q.defer();

    // create the context
    const context = require('rabbit.js').createContext(process.env.AMQP_URI || 'amqp://dev:66f8783cb5cb4045b8ce66f79177b819@localhost:5672');

    // create a socket
    locals.push = context.socket('PUSH', { noCreate: true });

    // connect!
    locals.push.connect(process.argv[2], () => deferred.resolve());

    // listen for the close event (exit the process when done)
    locals.push.on('close', () => process.exit(0));

    return deferred.promise;
};


const publishQueue = function () {
    // defer
    const deferred = Q.defer();

    // we want to pipe it back out (stdout)
    process.stdin.setEncoding('utf8');

    process.stdin
    .pipe(Split())
    .pipe(locals.push)
    .on('end', () => {

        // we are done!
        locals.push.end();
    });

    // return the promise
    return deferred.promise;
};

// kick it off!
Q(connectToRabbitMQStream())
.then(publishQueue)
.fail((err) => {

    console.log(err);
    process.exit(1);
});
