'use strict';

// this will pull down the entire contents of a queue without destroying or acknowledging the messages in a queue
// usage: pass in your AMQP_HOST
// usage: find the name of the queue that you want to peek
// usage: then run the script like:
// AMQP_HOST="some rmq" node rabbit-peek-queue.js some-queue
// the contents of the queue will go into a file named after the queue, ex: some-queue.json
const Amqp = require('amqplib/callback_api');
const Fs = require('fs');
const Q = require('q');

let _amqpConnection;
let _channel;

const options = {
    amqphost: process.env.AMQP_HOST || 'amqp://guest:guest@localhost'
};

const writeFile = Fs.createWriteStream(`${process.argv[2]}.json`);

const _openAmqpConnection = function () {

    const deferred = Q.defer();

    Amqp.connect(options.amqphost, (err, conn) => {

        if (err) {
            deferred.reject(err);
        }
        else {
            console.log('AMQP SUCCESS');
            _amqpConnection = conn;
            deferred.resolve();
        }
    });

    return deferred.promise;
};


const _createChannel = function () {

    const deferred = Q.defer();

    _amqpConnection.createChannel((err, ch) => {

        if (err) {
            deferred.reject(err);
        }
        else {
            console.log('AMQP CHANNEL CREATED');
            _channel = ch;
            deferred.resolve();
        }
    });

    return deferred.promise;
};


const _consume = function () {

    const deferred = Q.defer();


    _channel.consume(process.argv[2], (msg) => {

        const content = msg.content.toString();
        writeFile.write(`${content}\n`);
    });

    return deferred.promise;
};

_openAmqpConnection()
.then(_createChannel)
.then(_consume);
