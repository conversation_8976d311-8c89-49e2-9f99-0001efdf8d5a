const axios = require('axios').default;
const R = require('ramda');

const baseURL = 'https://qa-business-api-east-1b.golfnowcentral.com';
const Cookie = '';

const payloadToChange = {
    settings: {
        bookingEngineFive: {
            web: {
                newCheckoutFlow: true,
                checkIn: true,
                checkInWindow: 24,
                shoppingCart: {
                    enabled: true,
                    featuredProducts: true
                }
            }
        },
    },
}

const ids = [];

const apiManageClient = axios.create({
    baseURL,
    headers: { <PERSON><PERSON> }
});

const fetchAndUpdateEntity = async (id) => {
    try {
        const { data: currentCourse } = await apiManageClient.get(`/entity/course/${id}`);

        const updatedPayload = R.mergeDeepRight(currentCourse, payloadToChange);

        const { data: updatedCourse } = await apiManageClient.put('/course', updatedPayload);

        console.log(`${updatedCourse.name} course updated`);

        return { updated: true };
    } catch (error) {
        return { updated: false, message: error.message };
    }
};

const updateCourses = async () => {
    let failed = 0;
    let succeded = 0;
    
    for (let i = 0; i < ids.length; i++) {
        const result = await fetchAndUpdateEntity(ids[i]);
        if (result.updated) {
            console.log(`Courses updated ${++succeded} ${ids[i]}`);
        }
        else {
            console.log(`Courses failed to update ${++failed} ${ids[i]}`);
        }
    }
}

updateCourses();
