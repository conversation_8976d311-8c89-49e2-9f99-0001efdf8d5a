require('dotenv').config({ path: '.env' });

const fs = require('fs');
const aws = require('aws-sdk');
const axios = require('axios').default;
const write = require('@fast-csv/format');
const cliProgress = require('cli-progress');
const mime = require('mime-types');

const mediaGalleryAwsRegion = process.env.AWS_REGION;
const mediaGalleryAwsPublicKey = process.env.AWS_PUBLIC_KEY;
const mediaGalleryAwsSecretKey = process.env.AWS_SECRET_KEY;
const mediaGalleryAwsBucket = process.env.AWS_BUCKET;

const gatewayUrl = process.env.GATEWAY_URL;
const channelPassword = process.env.CHANNEL_PASSWORD;
const channelUsername = process.env.CHANNEL_USERNAME;

const awsEntityMediaLibraryUrl = process.env.MEDIA_LIBRARY_IMAGES_AWS_BASE_URL;

const csvPath = process.env.CSV_PATH;
const courseImagesPath = process.env.COURSE_IMAGES_DIRECTORY;

const extensions = new Set([
	'3dv',
	'ai',
	'amf',
	'art',
	'ase',
	'awg',
	'blp',
	'bmp',
	'bw',
	'cd5',
	'cdr',
	'cgm',
	'cit',
	'cmx',
	'cpt',
	'cr2',
	'cur',
	'cut',
	'dds',
	'dib',
	'djvu',
	'dxf',
	'e2d',
	'ecw',
	'egt',
	'emf',
	'eps',
	'exif',
	'fs',
	'gbr',
	'gif',
	'gpl',
	'grf',
	'hdp',
	'heic',
	'heif',
	'icns',
	'ico',
	'iff',
	'int',
	'inta',
	'jfif',
	'jng',
	'jp2',
	'jpeg',
	'jpg',
	'jps',
	'jxr',
	'lbm',
	'liff',
	'max',
	'miff',
	'mng',
	'msp',
	'nef',
	'nitf',
	'nrrd',
	'odg',
	'ota',
	'pam',
	'pbm',
	'pc1',
	'pc2',
	'pc3',
	'pcf',
	'pct',
	'pcx',
	'pdd',
	'pdn',
	'pgf',
	'pgm',
	'PI1',
	'PI2',
	'PI3',
	'pict',
	'png',
	'pnm',
	'pns',
	'ppm',
	'psb',
	'psd',
	'psp',
	'px',
	'pxm',
	'pxr',
	'qfx',
	'ras',
	'raw',
	'rgb',
	'rgba',
	'rle',
	'sct',
	'sgi',
	'sid',
	'stl',
	'sun',
	'svg',
	'sxd',
	'tga',
	'tif',
	'tiff',
	'v2d',
	'vnd',
	'vrml',
	'vtf',
	'wdp',
	'webp',
	'wmf',
	'x3d',
	'xar',
	'xbm',
	'xcf',
	'xpm',
]);

const isImage = (filePath) => {
    const mimeType = mime.lookup(`${courseImagesPath}/${filePath}`) || 'application/octet-stream';

    return extensions.has(mimeType.split('/')[1].toLowerCase());
}

const apiGatewayClient = axios.create({
    baseURL: gatewayUrl,
    headers: {
        password: channelPassword,
        username: channelUsername,
    }
});

const setupMediaGalleryConnection = () => {
    return new aws.S3({
        accessKeyId: mediaGalleryAwsPublicKey,
        secretAccessKey: mediaGalleryAwsSecretKey,
        region: mediaGalleryAwsRegion,
    });
};

const getCourse = async (facilityId) => {
    return apiGatewayClient.get(`/courses/search/third-party/golfnow/${facilityId}`)
        .then((res) => res.data);
}

const writeToCsv = async (data, headers) => {
    const fileStream = fs.createWriteStream(csvPath, { flags: 'a' });

    await new Promise((res, rej) => {
        write.writeToStream(fileStream, data, {
            headers,
            includeEndRowDelimiter: true
        })
        .on('error', err => rej(err))
        .on('finish', () => res());
    });
};

const readCourseFilesDirectory = async (directory) => {
    const files = await fs.promises.readdir(directory, { recursive: true });

    return files.filter((file) => isImage(file)).map((file) => {
        const filePath = file.toString();
        const regex = new RegExp('/.*Logo.*/', 'i');
        const isLogo = regex.test(filePath);
        const facilityId = filePath.match(/.*?(\d+)(?:\d+)?.*/)[1];
        const fileSource = fs.readFileSync(`${courseImagesPath}/${filePath}`, { encoding: 'base64' }).toString();
        const mimeType = mime.lookup(`${courseImagesPath}/${filePath}`) || 'application/octet-stream';

        return {
            type: isLogo ? 'logo' : 'image',
            facilityId,
            fileSource,
            fileName: file.split('/').pop().replace(/[&\/\\#,+()$~%'"`:*?<>{}\s]+/g, ''),
            mimeType,
        };
    })
};

/**
 * 
 * @returns {Promise<aws.S3.PutObjectOutput>}
 */
const uploadMediaLibraryImage = async ({ entityId, fileName, mimeType, fileSource }) => {
    const s3CLient = setupMediaGalleryConnection();
    const buf = Buffer.from(fileSource.replace(/^data:image\/\w+;base64,/, ''), 'base64');

    const awsKey = `entity-assets/${entityId}/course/${fileName}`;
    const params = {
        Bucket: mediaGalleryAwsBucket,
        Body: buf,
        ContentEncoding: 'base64',
        ContentType: mimeType,
        ACL: 'public-read',
        Key: awsKey,
    };

    const data = await s3CLient.putObject(params).promise();

    return data;
};

(async () => {
    console.log(`Reading ${courseImagesPath} directory...`);
    const images = await readCourseFilesDirectory(courseImagesPath);

    if (images.length === 0) {
        console.log('No files found!');
        process.exit(0);
    }

    const buildProgressBar = new cliProgress.SingleBar(
        {
            format: 'Building Csv Rows [{bar}] {percentage}% | ETA: {eta}s | {value}/{total} | Variable: {variable}',
        },
        cliProgress.Presets.shades_classic
    );

    buildProgressBar.start(images.length, 0);

    let failed = 0;
    const courseImages = [];
    for (const image of images) {
        try {
            const courseData = await getCourse(image.facilityId);

            if (!courseData) {
                console.error(`Course with facilityId ${image.facilityId} not found!`);

                return;
            }

            await uploadMediaLibraryImage({ entityId: courseData.id, fileName: image.fileName, mimeType: image.mimeType, fileSource: image.fileSource });

            courseImages.push([image.type, `${awsEntityMediaLibraryUrl}/entity-assets/${courseData.id}/course/${encodeURI(image.fileName)}`, image.facilityId]);    
        } catch (error) {
            console.error(error)
            ++failed;
        } finally {
            buildProgressBar.increment(1, { variable: image.fileName });
        }
    }

    buildProgressBar.stop();

    const headers = ['type', 'img_url', 'Course FID'];
    await write.writeToString(courseImages, { headers })
        .then((data) => console.log(data));

    await writeToCsv(courseImages, headers)
        .then(contents => {
            console.log('CSV file updated!');;
        })
        .catch(err => {
            console.error(err.stack);
            process.exit(1);
        });

    if (failed) {
        console.warn(`Failed to build rows for ${failed} courses!`);
    }

    process.exit(0);
})();
