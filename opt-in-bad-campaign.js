'use strict';

// this will opt in all customers that received an error and was opted out of a particular campaign
// usage: pass in your AMQP_HOST, CUSTOMERMONGO_HOST, and LEGACYMONGO_HOST
// usage: find the campaign id of the bad campaign, in this example: 5555aaaabbbbccccddddeeee
// usage: then run the script like:
// AMQP_HOST="some rmq", CUSTOMERMONGO_HOST="some mongo" LEGACYMONGO_HOST="some mongo" node opt-in-bad-campaigns.js 5555aaaabbbbccccddddeeee
// usage with file (note file assumses each campaign id is on a separate line):
// AMQP_HOST="some rmq", CUSTOMERMONGO_HOST="some mongo" LEGACYMONGO_HOST="some mongo" node opt-in-bad-campaigns.js file [somefile]
// AMQP_HOST="some rmq", CUSTOMERMONGO_HOST="some mongo" LEGACYMONGO_HOST="some mongo" node opt-in-bad-campaigns.js 5555aaaabbbbccccddddeeee

const Amqp = require('amqplib/callback_api');
const MongoClient = require('mongodb').MongoClient;
const ObjectId = require('mongodb').ObjectID;
const Q = require('q');
const _ = require('lodash');
const FS = require('fs');
const Async = require('async');

let _legacyDb;
let _customerDb;
let _amqpConnection;
let _channel;

const options = {
    amqphost: process.env.AMQP_HOST || 'amqp://guest:guest@localhost',
    customermongo: process.env.CUSTOMERMONGO_HOST || 'mongodb://**************',
    legacymongo: process.env.LEGACYMONGO_HOST || 'mongodb://**************'
};

const _openAmqpConnection = function () {

    const deferred = Q.defer();

    Amqp.connect(options.amqphost, (err, conn) => {

        if (err) {
            deferred.reject(err);
        }
        else {
            console.log('AMQP SUCCESS');
            _amqpConnection = conn;
            deferred.resolve();
        }
    });

    return deferred.promise;
};


const _createChannel = function () {

    const deferred = Q.defer();

    _amqpConnection.createChannel((err, ch) => {

        if (err) {
            deferred.reject(err);
        }
        else {
            console.log('AMQP CHANNEL CREATED');
            _channel = ch;
            deferred.resolve();
        }
    });

    return deferred.promise;
};


const _openLegacyDbConnection = function () {

    const deferred = Q.defer();

    MongoClient.connect(options.legacymongo, (err, db) => {

        if (err) {
            console.log(err);
            deferred.reject(err);
        }
        else {
            _legacyDb = db;
            console.log('LEGACY DB SUCCESS');
            deferred.resolve();
        }
    });

    return deferred.promise;
};


const _openCustomerDbConnection = function () {

    const deferred = Q.defer();

    MongoClient.connect(options.customermongo, (err, db) => {

        if (err) {
            console.log(err);
            deferred.reject(err);
        }
        else {
            _customerDb = db;
            console.log('CUSTOMER DB SUCCESS');
            deferred.resolve();
        }
    });

    return deferred.promise;
};

const _getFileContents = function () {

    const deferred = Q.defer();
    FS.readFile(process.argv[3], 'utf-8', (error, text) => {

        if (error) {
            deferred.reject(new Error(error));
        } else {
            deferred.resolve(text.split('\n'));
        }
    });
    return deferred.promise;
};

const _getFacilityCustomersWithCampaignId = function (campaignId) {

    const deferred = Q.defer();

    const collection = _legacyDb.collection('messages');

    collection.find({ 'metadata.campaignId': campaignId, error: { '$exists': 1 } }, { 'metadata.userId': 1 } ).toArray((err, result) => {

        if (err) {
            deferred.reject(err);
        }
        else {
            deferred.resolve(result);
            console.log('campaignId: ', campaignId);
            console.log('result: ', result.length);
        }
    });

    return deferred.promise;
};

const _getFacilityCustomers = function () {

    return _getFacilityCustomersWithCampaignId(process.argv[2]);
};

const _mapUserIds = function (args) {

    const deferred = Q.defer();

    const arr = _.map(args, (value, key) => {

        return ObjectId(value.metadata.userId);
    });

    deferred.resolve(arr);

    return deferred.promise;
};

const _getMasterCustomerIds = function (args) {

    const deferred = Q.defer();

    const collection = _customerDb.collection('customerfacilities');

    collection.find({ _id: { '$in': args } }, { facilityId: 1, customerId: 1 }).toArray((err, result) => {

        if (err) {
            console.log(err);
            deferred.reject(err);
        }
        else {
            deferred.resolve(result);
        }
    });

    return deferred.promise;
};


const _sendToQueue = function (args) {

    const deferred = Q.defer();

    for (let i = 0; i < args.length; ++i) {
        const obj = {
            method: 'PUT',
            path: '/customer/' + args[i].customerId.toString() + '/facility/' + args[i].facilityId.toString() + '?emailOptIn=true',
            payload: {}
        };

        _channel.sendToQueue('gn-api-customer', new Buffer(JSON.stringify(obj)), {});
        // console.log(JSON.stringify(obj))
    }

    deferred.resolve();

    return deferred.promise;
};

_openAmqpConnection()
.then(_createChannel)
.then(_openLegacyDbConnection)
.then(_openCustomerDbConnection)
.done(() => {

    if (process.argv[2] === 'file') {

        _getFileContents()
        .done((arr) => {

            const q = Async.queue((task, callback) => {

                if (task !== '') {

                    _getFacilityCustomersWithCampaignId(task)
                    .then(_mapUserIds)
                    .then(_getMasterCustomerIds)
                    .then(_sendToQueue)
                    // .delay(100)
                    .done(() => {

                        callback();
                    });
                } else {
                    callback();
                }
            }, 5);

            q.push(arr, () => {});

            q.drain = function () {

                console.log('All items have been processed. Script will terminate in 5 seconds...');
                _legacyDb.close();
                _customerDb.close();
                _amqpConnection.close();
                setTimeout(() => {

                    process.exit();
                }, 5000);
            };
        });
    } else {

        _getFacilityCustomers()
        .then(_mapUserIds)
        .then(_getMasterCustomerIds)
        .then(_sendToQueue)
        .delay(100)
        .done(() => {

            _legacyDb.close();
            _customerDb.close();
            _amqpConnection.close();
            console.log('Item has been processed. Script will terminate in 5 seconds...')
            setTimeout(() => {

                process.exit();
            }, 5000);
        });
    }
});
