'use strict';

const AMQP = require('amqplib');
const { MongoClient } = require('mongodb');
const Q = require('q');

const amqpUri = process.env.AMQP_URI || '*******************************';
const amqpConn = {};

const mongoUri = process.env.MONGO_URI || 'mongodb://192.168.99.1/gn-legacy-api';
let mongoDB = null;

const QUERY = {}; // default {} to return ALL course records

const amqpConnectionPromise = Q.defer();
const mongoConnectionPromise = Q.defer();

AMQP.connect( amqpUri )
    .then( (connection) => {

        amqpConn.connection = connection;
        return amqpConn.connection.createChannel();
    })
    .then( (channel) => {

        amqpConn.channel = channel;
        amqpConnectionPromise.resolve();
    })
    .catch( (connectError) => {

        console.log( 'AMQP connection error:', connectError );
        amqpConnectionPromise.reject( connectError );
    });

MongoClient.connect( mongoUri )
    .then( (db) => {

        console.log( 'remote connected' );
        mongoDB = db;

        mongoConnectionPromise.resolve();
    })
    .catch( (connectError) => {

        console.log( 'Mongo connection error', connectError );
        mongoConnectionPromise.reject( connectError );
    });


Q.all( [amqpConnectionPromise.promise, mongoConnectionPromise.promise] )
    .then( () => {

        const Stream = mongoDB.collection( 'courses' ).find( QUERY ).stream();
        let total = 0;


        Stream.on( 'data', (chunk) => {

            total++;

            const rabbitPayload = {
                method: 'POST',
                path: `/courses/${chunk._id}/third-party/sync-gn-api-data`,
                payload: null
            };

            amqpConn.channel.sendToQueue( 'gn-legacy-api-low', new Buffer( JSON.stringify( rabbitPayload ) ), {} );
        });

        Stream.on( 'error', (err) => {

            console.log( 'stream error', err );
            process.exit(1);
        });

        Stream.on( 'end', () => {

            amqpConn.channel.close()
                .then( () => amqpConn.connection.close() )
                .then( () => mongoDB.close() )
                .then( () => {

                    console.log( 'we\'re all done here', total );
                })
                .catch( (error) => {

                    console.log( 'there was an error closing connections:', error );
                });
        });
    })
    .catch( (connectError) => {

        process.exit( 1 );
    });
