// usage: cat golfnow-rateset-feed-error.txt | node file-stream.js

'use strict';
const HL = require('highland');
const R = require('ramda');
const { MongoClient } = require('mongodb');
const Q = require('q');

const localMongoURI = process.env.LOCAL_MONGO_URI || 'mongodb://192.168.99.1:27017/prod-data';
let localDb = null;
const localConnectionPromise = Q.defer();

MongoClient.connect( localMongoURI )
    .then( (db) => {

        console.log( 'local connected' );
        localDb = db;
        localConnectionPromise.resolve();
    })
    .catch( (err) => {
        localConnectionPromise.reject( err );
    });


const getFacilityId = async (referenceId) => {

    const facilityResult = await localDb.db('prod-data')
        .collection( 'courses' )
        .findOne(
            { _id: referenceId } , { projection: { 'thirdParty.id': 1 } } 
        );

    if (facilityResult) {
        return facilityResult.thirdParty.id;
    }

    return null;
}

const getMcoIdAndAlias = async (referenceId) => {

    const mcoResult = await localDb.db('prod-data')
        .collection('organizations')
        .findOne(
            { _id: referenceId }, { projection: { 'alias': 1 } } 
        );

    let alias = null;
    if(mcoResult) {
        alias = mcoResult.alias;
    } 
    return [referenceId, alias];
}

const getGroupFields = async (referenceId) => {

    const groupResult = await localDb.db('prod-data')
        .collection('groups')
        .findOne(
            { _id: referenceId }, { projection: { 'alias': 1, 'organizationId': 1 } }
        );

    let alias = null;
    let groupMcoAlias = null;
    let groupMcoId = null;
    if (groupResult) {
        alias = groupResult.alias;
        [groupMcoId, groupMcoAlias] = await getMcoIdAndAlias(groupResult.organizationId.toString());
    }
    return [referenceId, groupMcoAlias, alias];
}

const parseLog = async (line) => {
    const Regexp = /https:\/\/?([^.]+)\.(book\.|book-v2\.)teeitup\.com/gm;
    let match = Regexp.exec(line);

    let parsed = null;
    if (match) {
        const engine = match[2] == 'book.' ? '5.0' : '5.1';

        const aliasResult = await localDb.db('prod-data')
            .collection( 'aliases' )
            .findOne(
                { alias: match[1] }
            );

        let groupMcoAlias = null; 
        let alias = null;
        let id = null;
        const type = aliasResult.actor;
        switch (type) {
            case 'course':
                id = await getFacilityId(aliasResult.referenceId);
                alias = match[1];
                break;
            case 'mco':
                [id, alias] = await getMcoIdAndAlias(aliasResult.referenceId.toString());
                break;
            case 'mco-group':
                [id, groupMcoAlias, alias] = await getGroupFields(aliasResult.referenceId.toString());
                break;
            default:
                console.log("Who dis?", aliasResult.actor);
                break;
        }

        parsed = { type: type, id: id, alias: alias, engine: engine, groupMcoAlias: groupMcoAlias  };
    }
    
    return parsed;

}

const writeRecord = async (record) => {
    try {
        const writeResult = await localDb.db('prod-data')
        .collection( 'alias_to_engine')
        .insertOne(record);
        console.log("Successfully wrote ", record);
    }
    catch (err) {
        console.log("Error: ", err);
    }
}

const run = async function () {

    console.log( 'starting ...' );

    process.stdin.setEncoding( 'utf8' );

    HL( process.stdin )
        .split()
        .consume( async function (err, line, push, next) {

            if (err) {
                push( err );
                next();
            }
            else if (line === HL.nil) {
                push( null, line );
            }
            else {

                if (!line) {
                    push( null, line );
                    next();
                    return;
                }

                const data = await parseLog(line);
                if(data) {
                    writeRecord(data);
                }
                push( null, line );
                next();
            }
        })
        .done( () => {
            localDb.close();
        } );

};

// kick it off!
run();
