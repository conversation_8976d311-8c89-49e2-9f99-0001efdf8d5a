import { pathEq, pathOr, uniq } from 'ramda';
import util from 'util';
import { exec as callbackExec } from 'child_process';

const exec = util.promisify(callbackExec);

export const REPOS_ALL = 'All';
export const PICK_REPOS = 'Pick repositories';

export const processJiraIssues = async (jiraClient, jiraIssues) => {
  let repoNames = [];
  const svcIssues = {};

  for (const jiraIssue of jiraIssues) {
    const issueDetails = pathOr(
      [],
      ['detail'],
      await jiraClient.getDevStatusDetail(jiraIssue.id, 'GitHub', 'branch'),
    );

    const prs = issueDetails.flatMap((d) => d.pullRequests);

    for (const pr of prs) {
      const repoName = pr.url.match(/^https:\/\/github.com\/(.*)\/pull\/.*$/)[1];
      repoNames.push(repoName);

      if (!svcIssues[repoName]) {
        svcIssues[repoName] = [];
      }

      svcIssues[repoName].push(jiraIssue.key);
    }
  }

  repoNames = uniq(repoNames);

  return {
    repoNames,
    svcIssues,
  };
};

export const createGitHubBranch = async (
  Octo,
  repo,
  sourceBranch,
  targetBranch,
) => {
  const refs = await Octo.request('GET /repos/{owner}/{repo}/git/refs/heads', {
    owner: 'GolfNowEng',
    repo: repo.github,
  });

  if (refs.status !== 200) {
    console.log(JSON.stringify(refs, undefined, 2));
    throw new Error('Get head failure');
  }

  const sourceHead = refs.data.find((head) => head.ref === `refs/heads/${sourceBranch}`);
  const newHead = refs.data.find((head) => head.ref === `refs/heads/${targetBranch}`);

  if (!sourceHead) {
    throw new Error(`Source branch "${sourceBranch}" not found.`);
  } else if (newHead) {
    console.warn(`Target branch "${targetBranch}" already exists.`);
  } else {
    const newBranch = await Octo.request('POST /repos/{owner}/{repo}/git/refs', {
      owner: 'GolfNowEng',
      repo: repo.github,
      ref: `refs/heads/${targetBranch}`,
      sha: sourceHead.object.sha,
    });

    if (newBranch.status !== 201) {
      console.log(JSON.stringify(newBranch, undefined, 2));
      throw new Error('Create branch failure');
    }
  }
};

export const createGitHubPR = async (Octo, repo, selectedVersion) => {
  const pullRequest = await Octo.request('POST /repos/{owner}/{repo}/pulls', {
    owner: 'GolfNowEng',
    repo: repo.github,
    head: `release/${selectedVersion}`,
    base: 'master',
    body: 'PR for release.',
    title: 'Merge release branch',
  });

  if (pullRequest.status !== 201) {
    throw new Error('Create Pull Request Failure');
  }

  return pullRequest.data.html_url;
};

export const createGitHubRelease = async (Octo, repo, selectedVersion, newImage, ticketsDesc) => {
  const tagName = newImage.replace(/^.*:/, '');

  const createReleaseResp = await Octo.repos.createRelease({
    body: `# ${selectedVersion}\nProduction Release\n\n${ticketsDesc}`,
    draft: true,
    name: tagName,
    owner: 'GolfNowEng',
    prerelease: false,
    repo: repo.github,
    tag_name: tagName,
    target_commitish: 'master',
  });

  if (createReleaseResp.status !== 201) {
    console.error(JSON.stringify(createReleaseResp, undefined, 2));
    throw new Error('Create Github Release Failure');
  }

  return createReleaseResp.data.html_url;
};

const changeK8sContext = async (contextName) => {
  let context;
  switch (contextName) {
    case 'PROD':
      context = 'gke_golfnow-b2b_us-east1_b2b-001-us-east1';
      break;

    case 'STAGE':
      context = 'gke_golfnow-staging_us-east1-b_golfnow-staging-us-east1-b';
      break;

    case 'QA':
    default:
      context = 'gke_golfnow-b2b-qa_us-east1-b_golfnow-b2b-qa-001-us-east1';
  }

  return exec(`kubectl config use-context ${context}`);
};

const getDeployImageName = async (k8sDeployName, context) => {
  await changeK8sContext(context);
  return (await exec(`kubectl get deploy -o yaml ${k8sDeployName} | grep image:`)).stdout;
};

export const getDockerImages = async (repo) => {
  let stdout;
  let rollBackImage = '';
  if (!repo.k8s) {
    return { rollBackImage, newImage: '' };
  }
  if (!repo.firstRelease) {
    stdout = await getDeployImageName(repo.k8s, 'PROD');
    rollBackImage = stdout.replace('f:image: {}', '').trim().replace('image: ', '');
  } else {
    rollBackImage = 'NO ROLLBACK IMAGE - FIRST RELEASE OF SERVICE';
  }

  stdout = await getDeployImageName(repo.k8s, 'STAGE');
  const newImage = stdout.replace('f:image: {}', '').trim().replace('image: ', '');

  return { rollBackImage, newImage };
};

export const isVersionDeployed = async (releaseName, repo, context) => {
  const imageName = await getDeployImageName(repo.k8s, context);
  return imageName.indexOf(releaseName) > -1;
};

export const deleteMergedDraftBranch = async (Octo, repo, sourceBranch) => {
  await Octo.request(`DELETE /repos/{owner}/{repo}/git/refs/heads/${sourceBranch}`, {
    owner: 'GolfNowEng',
    repo: repo.github,
  });
};

export const getAllTickets = async (jiraClient, component, searchVersions) => {
  const componentQueryString = component === REPOS_ALL || component === PICK_REPOS ? '' : `AND component="${component}"`;
  let allIssues = [];
  let currentIssues = [];
  let iterator = 0;

  do {
    currentIssues = (
      await jiraClient.searchJira(`project=GNCBE ${componentQueryString} AND fixVersion IN ("${searchVersions.join('", "')}")`, { maxResults: 100, startAt: iterator === 0 ? 0 : (iterator * 100) - 1 })
    ) || { issues: [] };
    allIssues = allIssues.concat(currentIssues.issues);

    iterator++;
  } while (currentIssues.issues.length >= 100);

  return allIssues.filter(
    (issue) => !pathEq(['fields', 'status', 'name'], 'Closed', issue),
  );
};

export const loading = (message = '') => {
  const h = ['|', '/', '-', '\\'];
  let i = 0;

  return setInterval(() => {
    i = (i > 3) ? 0 : i;
    console.clear();
    console.log(`${h[i]} ${message}`);
    i++;
  }, 200);
};
