{"name": "atlassian-api", "version": "2.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"start": "node ./index.js", "release:cut": "node ./index.js --cutRelease", "release:docs": "node ./index.js --prepareReleaseDocs"}, "author": "<EMAIL>", "license": "ISC", "dependencies": {"@kubernetes/client-node": "^0.12.2", "@octokit/rest": "^18.0.6", "dotenv": "^16.0.1", "enquirer": "^2.3.6", "jira-client": "^8.1.0", "ramda": "^0.27.1"}, "devDependencies": {"@octokit/types": "^5.5.0", "@types/jira-client": "^7.1.4", "eslint-config-airbnb": "^19.0.4"}}