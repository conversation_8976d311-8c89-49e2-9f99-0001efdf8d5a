import JiraClient from 'jira-client';
import enquirer from 'enquirer';

import RepoData from './repo-data.js';
import {
  processJiraIssues,
  getAllTickets,
  REPOS_ALL,
  loading,
  getDockerImages,
} from './servant.js';

const {
  Select,
} = enquirer;

const SKIP_REPOS = Object.keys(RepoData)
  .filter((key) => !!RepoData[key].skip)
  .reduce((acc, key) => acc.concat(key), []);

const checkReleaseStatus = async () => {
  let jiraIssues = [];

  const jiraClient = new JiraClient({
    protocol: 'https',
    host: 'golfnow.atlassian.net',
    username: process.env.JIRA_USERNAME,
    password: process.env.JIRA_TOKEN,
  });

  const { id: projectId } = await jiraClient.getProject('GNCBE');
  const existingVersions = await jiraClient.getVersions(projectId);

  const availableFixVersions = existingVersions
    .filter(
      (v) => !v.released
        && !v.archived
        && v.name !== 'next',
    )
    .map((v) => v.name);

  if (!availableFixVersions.length) {
    console.warn('There are no available versions');
    return;
  }

  const selectedVersion = await new Select({
    message: 'Select version:',
    choices: availableFixVersions,
  }).run();

  // eslint-disable-next-line prefer-destructuring
  jiraIssues = await getAllTickets(jiraClient, REPOS_ALL, [selectedVersion]);

  const loader = loading(`Getting repo names for ${selectedVersion}...`);

  // eslint-disable-next-line max-len
  const { repoNames } = await processJiraIssues(
    jiraClient,
    jiraIssues,
  );

  clearInterval(loader);
  console.clear();

  // Check for backmerge first
  for (const repoName of repoNames) {
    if (SKIP_REPOS.includes(repoName)) {
      continue;
    }

    if (!RepoData[repoName]) {
      throw new Error(`REPO NOT FOUND: ${repoName}`);
    }

    const repo = RepoData[repoName];
    const { newImage } = await getDockerImages(repo);

    console.log(`${newImage.indexOf(selectedVersion) > -1 ? '\u2713' : 'X'} - ${repoName} - ${newImage}`);
  }
};

export default checkReleaseStatus;
