1. Get github token
2. Get atlassian token
> Encode token in base64 username:token
> Basic base64Token
3. Change fix version
3.  Change fix version
4. Tun on" debug mode
5. Change document headers
> Release date/ time and engineer
6. Determine what kind of release - marketing, BE or both
7. Change kubernetes context to staging
8. Run in "debug" mode to verify all the tickets are correct
9. Run in "normal" mode to create releases and PRs into master
10. Edit the order of services to acccount for dependencies etc
11. Break out internal release notes into Pre/Post release sections
> Procure configs if needed
12. Merge each PR
> Merge should requires at least 1 review
> 
> Check for missing config changes not reflected in the internal release notes (v important)
