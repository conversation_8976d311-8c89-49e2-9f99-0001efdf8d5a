/* eslint-disable import/prefer-default-export */
const getReleaseDate = (branchName) => {
  const match = branchName.match(/\d{4}\.\d{2}\.\d{2}/);

  return match ? match[0] : '';
};

const compareBranches = ({ name: branch1 }, { name: branch2 }) => {
  if (branch1 === 'master' || branch1 === 'main') {
    return -1;
  }
  if (branch2 === 'master' || branch2 === 'main') {
    return 1;
  }
  if (branch1 === 'develop') {
    return 1;
  }
  if (branch2 === 'develop') {
    return -1;
  }

  const branch1Date = getReleaseDate(branch1);
  const branch2Date = getReleaseDate(branch2);

  if (branch2Date === branch1Date) {
    return 0;
  }

  return branch2Date > branch1Date ? -1 : 1;
};

const listReleaseBranchesForRepo = async (Octo, owner, repo) => {
  const { data: allBranches } = await Octo.repos.listBranches({ owner, repo });

  return allBranches
    .filter(({ name }) => name && name.match(/(^master$|^main$|^develop$|^release_|^release\/|^draft_release)/))
    .sort(compareBranches);
};

export const checkForRequiredBackmerge = async (Octo, reponame) => {
  const [owner, repo] = reponame.split('/');

  const releaseBranches = await listReleaseBranchesForRepo(Octo, owner, repo);

  for (let i = 0; i < releaseBranches.length - 1; i++) {
    const { data: diff } = await Octo.repos.compareCommits({
      owner,
      repo,
      base: releaseBranches[i + 1].commit.sha,
      head: releaseBranches[i].commit.sha,
    });

    if (diff.files.length) {
      console.warn(`!!! There are non back-merged changes in ${reponame} from ${releaseBranches[i].name} to ${releaseBranches[i + 1].name}`);
      console.warn('Please make a back-merge before trying again');

      process.exit();
    }
  }
};
