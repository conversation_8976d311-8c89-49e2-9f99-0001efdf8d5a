import JiraClient from 'jira-client';
import { Octokit } from '@octokit/rest';
import enquirer from 'enquirer';

import { checkForRequiredBackmerge } from './backMergeCheck.js';
import RepoData from './repo-data.js';
import {
  createGitHubPR,
  createGitHubRelease,
  getAllTickets,
  getDockerImages,
  processJiraIssues,
} from './servant.js';

const { Select, MultiSelect, prompt } = enquirer;

const SKIP_REPOS = Object.keys(RepoData)
  .filter((key) => !!RepoData[key].skip)
  .reduce((acc, key) => (acc.concat(key)), []);

const prepareReleaseDocs = async (dryRun = false) => {
  const jiraClient = new JiraClient({
    protocol: 'https',
    host: 'golfnow.atlassian.net',
    username: process.env.JIRA_USERNAME,
    password: process.env.JIRA_TOKEN,
  });

  const Octo = new Octokit({
    auth: process.env.OCTOKIT_TOKEN,
    baseUrl: 'https://api.github.com',
    userAgent: 'phx-release-script',
  });
  const projectId = (await jiraClient.getProject('GNCBE')).id;
  const existingVersions = (await jiraClient.getVersions(projectId))
    .filter((v) => !v.released && !v.archived && v.name !== 'next')
    .map((v) => v.name);

  const selectedVersion = await (new Select({
    message: 'Select version:',
    choices: existingVersions,
  })).run();

  const tomorrow = new Date();
  tomorrow.setDate((new Date()).getDate() + 1);

  const devOpsLeads = await (new MultiSelect({
    message: 'DevOps Leads:',
    choices: process.env.DEVOPS_LEADS.split(','),
  })).run();

  const engineeringLeads = await (new MultiSelect({
    message: 'Engineering Leads:',
    choices: process.env.ENGINEERING_LEADS.split(','),
  })).run();

  const releaseDate = (await prompt({
    type: 'input',
    name: 'value',
    message: 'Release date (Tab to autocomplete):',
    initial: `${tomorrow.toDateString()} @ 04:30 ET`,
  })).value;

  console.log('\n----------------------------------------------------------\n');
  console.log(`# ${releaseDate}`);
  console.log(`- DevOps Leads: ${devOpsLeads.join(', ')}`);
  console.log(`- Engineering Leads: ${engineeringLeads.join(', ')}`);
  console.log('- Slack: #b2b-marketing\n');

  const allIssues = await getAllTickets(jiraClient, 'All', [selectedVersion]);

  console.log(`---\n# TICKETS (${allIssues.length})`);

  let releaseNotes = '';

  allIssues.forEach(jiraIssue => {
    if (jiraIssue.fields['customfield_16800']) {
      releaseNotes += `> ${jiraIssue.key} - ${jiraIssue.fields['customfield_16800']}\n\n`;
    }
    console.log(`#### ${jiraIssue.key} (${jiraIssue.fields.status.name})\n- ${jiraIssue.fields.summary}\n`);
  });

  if (releaseNotes.length > 0) {
    console.log(`---\n# RELEASE NOTES`);
    console.log(releaseNotes);
  }

  const { repoNames, svcIssues } = await processJiraIssues(
    jiraClient,
    allIssues,
    dryRun,
  );

  for (const repoName of repoNames) {
    const repo = RepoData[repoName];

    if (SKIP_REPOS.includes(repoName)) {
      continue;
    }

    if (!repo) {
      throw new Error(`REPO NOT FOUND: ${repoName}`);
    }

    await checkForRequiredBackmerge(Octo, repoName);

    if (!repo.microFrontEnd && (!repo.k8s || !repo.jenkins)) {
      continue;
    }
    console.log(`\n## ${repoName}`);

    const ticketsDesc = allIssues
      .filter((issue) => svcIssues[repoName].includes(issue.key))
      .reduce((acc, issue) => `${acc}- [${issue.key}]  ${issue.fields.summary}\n`, '');

    if (repo.microFrontEnd) {
      if (!dryRun) {
        const prLink = await createGitHubPR(Octo, repo, selectedVersion);
        console.log(`- Pull Request: ${prLink}`);
      } else {
        console.log(`Release body:\n# ${selectedVersion}\nProduction Release\n\n${ticketsDesc}`);
      }
    } else {
      const { rollBackImage, newImage } = await getDockerImages(repo);

      console.log(`- Rollback Image: ${rollBackImage}`);
      console.log(`- New Image: ${newImage}`);

      if (!dryRun) {
        const prLink = await createGitHubPR(Octo, repo, selectedVersion);
        console.log(`- Pull Request: ${prLink}`);

        const releaseLink = await createGitHubRelease(
          Octo,
          repo,
          selectedVersion,
          newImage,
          ticketsDesc,
        );
        console.log(`- Release: ${releaseLink}`);
      } else {
        console.log(`Release body:\n# ${selectedVersion}\nProduction Release\n\n${ticketsDesc}`);
      }
    }
  }
};

export default prepareReleaseDocs;
