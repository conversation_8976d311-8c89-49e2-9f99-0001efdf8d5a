import JiraClient from 'jira-client';
import { Octokit } from '@octokit/rest';
import enquirer from 'enquirer';

import { pathOr } from 'ramda';
import RepoData from './repo-data.js';
import { checkForRequiredBackmerge } from './backMergeCheck.js';
import {
  createGitHubBranch,
  processJiraIssues,
  deleteMergedDraftBranch,
  getAllTickets,
  REPOS_ALL,
  PICK_REPOS,
  loading,
} from './servant.js';

const {
  Select, Confirm, MultiSelect, prompt,
} = enquirer;

const DRAFT_RELEASE = 'draft_release';
const DEVELOP = 'develop';
const RELEASE = 'release';

const COMPONENTS = [REPOS_ALL, PICK_REPOS];

const SKIP_REPOS = Object.keys(RepoData)
  .filter((key) => !!RepoData[key].skip)
  .reduce((acc, key) => acc.concat(key), []);

const pickSourceBranchType = async (targetBranchType) => {
  if (targetBranchType === DRAFT_RELEASE) {
    return DEVELOP;
  }

  const sourceBranchTypeSelect = new Select({
    message: 'Source branch type:',
    choices: [DEVELOP, DRAFT_RELEASE],
  });

  return sourceBranchTypeSelect.run();
};

const pickTargetBranchType = async () => {
  const targetBranchTypeSelect = new Select({
    message: 'Target branch type:',
    choices: [RELEASE, DRAFT_RELEASE],
  });

  return targetBranchTypeSelect.run();
};

const pickComponent = async () => {
  const componentSelect = new Select({
    message: 'Component:',
    choices: COMPONENTS,
  });

  const component = await componentSelect.run();

  return { component };
};

const cutRelease = async (dryRun = false) => {
  let newVersion;
  let selectedVersion;
  let jiraIssues = [];

  const jiraClient = new JiraClient({
    protocol: 'https',
    host: 'golfnow.atlassian.net',
    username: process.env.JIRA_USERNAME,
    password: process.env.JIRA_TOKEN,
  });

  const Octo = new Octokit({
    auth: process.env.OCTOKIT_TOKEN,
    baseUrl: 'https://api.github.com',
    userAgent: 'phx-release-script',
  });

  const { id: projectId } = await jiraClient.getProject('GNCBE');
  const existingVersions = await jiraClient.getVersions(projectId);

  const targetBranchType = await pickTargetBranchType();
  const sourceBranchType = await pickSourceBranchType(targetBranchType);

  let pickRepos;

  if (sourceBranchType === DRAFT_RELEASE) {
    const availableFixVersions = existingVersions
      .filter(
        (v) => !v.released
          && !v.archived
          && v.name !== 'next'
          && v.name.startsWith('draft/'),
      )
      .map((v) => v.name);

    if (!availableFixVersions.length) {
      console.warn('There are no draft_release fix versions available');
      return;
    }

    selectedVersion = await new Select({
      message: 'Select version:',
      choices: availableFixVersions,
    }).run();

    // eslint-disable-next-line prefer-destructuring
    newVersion = selectedVersion.split('/')[1];
    jiraIssues = await getAllTickets(jiraClient, REPOS_ALL, [selectedVersion]);
  } else {
    const searchVersions = ['next'];

    const { component } = await pickComponent();

    if (component === PICK_REPOS) {
      pickRepos = true;
    }

    const versionPrefix = targetBranchType === DRAFT_RELEASE ? 'draft/' : '';
    const releaseDate = new Date()
      .toISOString()
      .substring(0, 10)
      .replace(/-/g, '.');

    newVersion = (
      await prompt({
        type: 'input',
        name: 'value',
        message: 'New Version (Tab to autocomplete):',
        initial: `${versionPrefix}${releaseDate}`,
      })
    ).value;

    if (existingVersions.some((v) => v.name === newVersion)) {
      const continueToUseConfirm = new Confirm({
        message: `Version "${newVersion}" already exists. Continue to use it?`,
      });

      const continueToUse = await continueToUseConfirm.run();

      if (!continueToUse) {
        return;
      }

      searchVersions.push(newVersion);
    }

    jiraIssues = await getAllTickets(jiraClient, component, searchVersions);
  }

  // we do not cut draft from draft so draft_release/draft_release/version should not appear here
  const sourceBranch = sourceBranchType === DEVELOP ? DEVELOP : `draft_release/${newVersion}`;
  const targetBranch = `${targetBranchType}/${newVersion.replace(
    'draft/',
    '',
  )}`;

  let newJiraVersion = null;
  const releaseDescription = [];
  if (!dryRun) {
    newJiraVersion = await jiraClient.createVersion(
      {
        name: newVersion,
        projectId,
        startDate: new Date().toISOString().split('T')[0],
      },
      { notifyUsers: false },
    );
  }

  let loader = loading('Processing Jira Issues...');

  // eslint-disable-next-line max-len
  const { svcIssues, repoNames } = await processJiraIssues(
    jiraClient,
    jiraIssues,
    dryRun,
    newVersion,
  );

  let filteredRepos = repoNames;

  clearInterval(loader);
  console.clear();

  if (pickRepos) {
    filteredRepos = await (new MultiSelect({
      message: 'Repositories:',
      choices: repoNames,
    })).run();
  }

  loader = loading('Checking for backmerges...');

  // Check for backmerge first
  for (const repoName of filteredRepos) {
    if (SKIP_REPOS.includes(repoName)) {
      continue;
    }

    if (!RepoData[repoName]) {
      throw new Error(`REPO NOT FOUND: ${repoName}`);
    }

    await checkForRequiredBackmerge(Octo, repoName);
  }

  clearInterval(loader);
  console.clear();

  let releaseNotesFormatted = '';

  console.log('\n# SERVICES');

  for (const repoName of filteredRepos) {
    const repo = RepoData[repoName];

    const ticketsForRepo = jiraIssues.filter((issue) => svcIssues[repoName].includes(issue.key));

    for (const ticket of ticketsForRepo) {
      const releaseNotes = pathOr(null, ['fields', 'customfield_16800'], ticket);

      if (releaseNotes) {
        releaseNotesFormatted += `> ${ticket.key} - ${releaseNotes}\n`;
      }

      if (newVersion && !dryRun) {
        await jiraClient.updateIssue(ticket.key, {
          update: {
            fixVersions: [
              { set: [{ name: newVersion }] },
            ],
          },
        }, {
          notifyUsers: false,
        });
      }
    }

    const ticketKeys = ticketsForRepo.map((issue) => issue.key);

    console.log(`${repoName} (${ticketKeys.join(', ')})`);
    releaseDescription.push(`${repoName} (${ticketKeys.join(', ')})`);

    if (!dryRun) {
      await createGitHubBranch(Octo, repo, sourceBranch, targetBranch);
      if (sourceBranchType === DRAFT_RELEASE && targetBranchType === RELEASE) {
        await deleteMergedDraftBranch(Octo, repo, sourceBranch);
      }
    }
  }

  if (releaseNotesFormatted) {
    console.log(`\n## Release Notes:\n${releaseNotesFormatted}\n`);
  }

  if (!dryRun) {
    if (sourceBranchType === DRAFT_RELEASE && targetBranchType === RELEASE) {
      const versionToDelete = existingVersions.find(
        (v) => v.name === selectedVersion,
      ).id;
      await jiraClient.deleteVersion(versionToDelete);
    }

    if (!!newJiraVersion === true) {
      await jiraClient.updateVersion({
        id: newJiraVersion.id,
        description: releaseDescription.toString().replaceAll('),', ')\n'),
      });
    }
  }
};

export default cutRelease;
