import 'dotenv/config';
import enquirer from 'enquirer';

import cutRelease from './cutRelease.js';
import prepareReleaseDocs from './prepareReleaseDocs.js';
import checkReleaseStatus from './checkReleaseStatus.js';

const { Select } = enquirer;

const versionDeploymentStatus = 'Version Deployment Status';

(async () => {
  let dryRun = process.argv.some((a) => a === '--dryRun');
  if (!dryRun) {
    dryRun = (await (new Select({
      message: 'Run mode:',
      choices: ['Dry run', 'Normal'],
    })).run()) === 'Dry run';
  }

  if (process.argv.some((a) => a === '--cutRelease')) {
    await cutRelease(dryRun);
    return;
  }

  if (process.argv.some((a) => a === '--prepareReleaseDocs')) {
    await prepareReleaseDocs(dryRun);
    return;
  }

  const action = await (new Select({
    message: 'Action:',
    choices: ['Cut Release', 'Prepare Release Docs', versionDeploymentStatus],
  })).run();

  switch (action) {
    case 'Cut Release':
      await cutRelease(dryRun);
      break;
    case 'Prepare Release Docs':
      await prepareReleaseDocs(dryRun);
      break;
    case versionDeploymentStatus:
      await checkReleaseStatus(dryRun);
      break;
    default:
      throw new Error('Invalid action');
  }

  process.exit(0);
})().catch((e) => {
  console.error(e);
  process.exit(1);
});
