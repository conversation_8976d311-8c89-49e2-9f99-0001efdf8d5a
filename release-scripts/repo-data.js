const TEST_AREA = {
  // BE
  BE_USER_PROFILE: 'Booking Engine: USER PROFILE',
  BE_API_2_1: 'Booking Engine: API 2.1',
  BE_CHECKOUT_LOYALTY_CONFIRMATION: 'Booking Engine: CHECKOUT, LO<PERSON>ALTY- CONFIRMATION',
  BE_RULES: 'Booking Engine: RULES',
  BE_CHECK_IN: 'Booking Engine: CHECK-IN',
  BE_INVITE_A_FRIEND: 'Booking Engine: INVITE A FRIEND',
  BE_SHOPPING_CART: 'Booking Engine: SHOPPING CART',
  BE_FEATURED_PRODUCTS: 'Booking Engine: FEATURED PRODUCTS',
  BE_G1_MEMBERSHIPS: 'Booking Engine: G1 MEMBERSHIPS',
  BE_WALLET: 'Booking Engine: WALLET',
  BE_CANCELLATION: 'Booking Engine: CANCELLATION',
  BE_MCO_GROUPS: 'Booking Engine: MCO, GROUPS',
  BE_MODERN_MANAGE_SETTINGS: 'Booking Engine: M<PERSON>ER<PERSON> MANAGE SETTINGS',
  // Modern Manage
  MM_AUTHENTICATION: 'Modern Manage: Authentication',
  MM_ADMIN_MANAGE_FACILITY_GROUPS: 'Modern Manage: Admin - Manage Facility Groups',
  MM_ADMIN_MANAGE_SYSTEM_USERS: 'Modern Manage: Admin - Manage System Users',
  MM_ADMIN_MANAGE_SYSTEM_SETTINGS: 'Modern Manage: Admin - Manage System Settings',
  MM_ACCOUNT_SETTINGS: 'Modern Manage: AccountSettings',
  MM_ELASTIC_SEARCH: 'Modern Manage: ElasticSearch',
  MM_HOME: 'Modern Manage: Home',
  MM_CAMPAIGNS_ONE_TIME_CAMPAIGNS: 'Modern Manage: Campaigns - ONE TIME CAMPAIGNS',
  MM_CAMPAIGNS_RECURRING_CAMPAIGNS: 'Modern Manage: Campaigns - RECURRING CAMPAIGNS',
  MM_CAMPAIGNS_MANAGE_FACILITY_GROUP_CAMPAIGNS: 'Modern Manage: Campaigns - MANAGE FACILITY GROUP CAMPAIGNS',
  MM_CAMPAIGNS_CAMPAIGN_ACTIONS: 'Modern Manage: Campaigns - CAMPAIGN ACTIONS',
  MM_FACILITY_SETTINGS_GENERAL: 'Modern Manage: FACILITY SETTINGS - General',
  MM_FACILITY_SETTINGS_SOCIAL_MEDIA: 'Modern Manage: FACILITY SETTINGS - Social Media',
  MM_FACILITY_SETTINGS_MERGE_FIELDS: 'Modern Manage: FACILITY SETTINGS - Merge Fields',
  MM_FACILITY_SETTINGS_MEDIA_LIBRARY: 'Modern Manage: FACILITY SETTINGS - Media Library',
  MM_FACILITY_SETTINGS_MERGE_IMAGES: 'Modern Manage: FACILITY SETTINGS - Merge Images',
  MM_FACILITY_SETTINGS_COURSE_APPS: 'Modern Manage: FACILITY SETTINGS - Course Apps',
  MM_FACILITY_SETTINGS_COURSE_MAPPINGS: 'Modern Manage: FACILITY SETTINGS - Course Mappings',
  MM_LOYALTY: 'Modern Manage: LOYALTY',
  MM_CUSTOMERS_LIST: 'Modern Manage: CUSTOMERS - CUSTOMER LIST',
  MM_CUSTOMERS_UPDATE: 'Modern Manage: CUSTOMERS - CUSTOMER UPDATE',
  MM_CUSTOMERS_CREATE: 'Modern Manage: CUSTOMERS - CUSTOMER CREATION',
  MM_CUSTOMERS_IMPORT: 'Modern Manage: CUSTOMERS - CUSTOMER IMPORT',
  MM_CUSTOMERS_TAGS: 'Modern Manage: CUSTOMERS - CUSTOMER TAGS',
  MM_CUSTOMERS_LISTS: 'Modern Manage: CUSTOMERS - CUSTOMER LISTS',
  MM_BE_SETTINGS_GENERAL: 'Modern Manage: Booking Engine Settings - General',
  MM_BE_SETTINGS_INVENTORY: 'Modern Manage: Booking Engine Settings - Inventory',
  MM_BE_SETTINGS_RATES: 'Modern Manage: Booking Engine Settings - Rates',
  MM_BE_SETTINGS_CHECKOUT: 'Modern Manage: Booking Engine Settings - Checkout',
  MM_BE_SETTINGS_APPEARANCE: 'Modern Manage: Booking Engine Settings - Appearance',
  MM_PROMOTIONS_UNIVERSAL: 'Modern Manage: Promotions - Universal',
  MM_PROMOTIONS_UNIQUE: 'Modern Manage: Promotions - Unique',
  MM_RESERVATION_CENTER_G1: 'Modern Manage: RESERVATION CENTER - G1 RES CENTER',
  MM_RESERVATION_CENTER_GNR: 'Modern Manage: RESERVATION CENTER - GNR RES CENTER',
  MM_TRANSACTIONAL_MESSAGING: 'Modern Manage: Transactional Messaging',
  MM_BEE_EDITOR: 'Modern Manage: BEE EDITOR',
  // Legacy Manage
  LM_GENERAL_CUSTOMERS: 'Legacy Manage: Management - General - CUSTOMERS',
  LM_GENERAL_RATES: 'Legacy Manage: Management - General - RATES',
  LM_GENERAL_SOCIAL_MEDIA: 'Legacy Manage: Management - General - SOCIAL MEDIA',
  LM_GENERAL_TAGS: 'Legacy Manage: Management - General - TAGS',
  LM_GENERAL_LOYALTY_PROGRAMS: 'Legacy Manage: Management - General - LOYALTY PROGRAMS',
  LM_SETTINGS_COURSE_SETTINGS: 'Legacy Manage: Management - Settings - COURSE SETTINGS',
  LM_SETTINGS_BOOKING_ENGINE_SETTINGS: 'Legacy Manage: Management - Settings - BOOKING ENGINE SETTINGS',
  LM_MARKETING_PROMOTIONS_CURRENT_EXPIRED_DELETED: 'Legacy Manage: Marketing-Promotions - CURRENT PROMOTIONS / EXPIRED / DELETED',
  LM_MARKETING_PROMOTIONS_CAMPAIGN_MANAGEMENT: 'Legacy Manage: Marketing-Promotions - CAMPAIGN MANAGEMENT',
  LM_MANAGE_MCO: 'Legacy Manage: MANAGE MCO',
  LM_OTHER: 'Legacy Manage: OTHER',
};
/**
 * @typedef {Object} repoData
 * @param {string} github - repo name in github GolfNowEng organization
 * @param {string} jenkins - jenkins build job name
 * @param {string} k8s - deployment name in k8s
 * @param {string[]} [testAreas] - list of test areas that this repo touches, optional
 * @param {boolean} [skip] - if true, will entirely skip this repo during branch cut/relase doc prep
 * @param {boolean} [firstRelease] - if true, will skip checking for current image in production env
 */

/**
 * @module repoData
 */
export default {
  'GolfNowEng/gn-api-crm': {
    github: 'gn-api-crm',
    jenkins: 'gn-api-crm',
    k8s: 'gn-api-crm-internal',
  },
  'GolfNowEng/gn-api-dewey': {
    github: 'gn-api-dewey',
    jenkins: 'gn-api-dewey',
    k8s: 'gn-api-dewey-courses-v7.12',
  },
  'GolfNowEng/gn-api-importer': {
    github: 'gn-api-importer',
    jenkins: 'gn-api-importer',
    k8s: 'gn-api-importer',
  },
  'GolfNowEng/gn-api-waldo': {
    github: 'gn-api-waldo',
    jenkins: 'gn-api-waldo',
    k8s: 'gn-api-waldo-internal',
    testAreas: [
      TEST_AREA.MM_RESERVATION_CENTER_G1,
      TEST_AREA.MM_RESERVATION_CENTER_GNR,
    ],
  },
  'GolfNowEng/gn-api-wonka': {
    github: 'gn-api-wonka',
    jenkins: 'gn-api-wonka',
    k8s: 'gn-api-wonka',
    testAreas: [
      TEST_AREA.LM_MARKETING_PROMOTIONS_CURRENT_EXPIRED_DELETED,
      TEST_AREA.MM_PROMOTIONS_UNIVERSAL,
      TEST_AREA.MM_PROMOTIONS_UNIQUE,
    ],
  },
  'GolfNowEng/api': {
    github: 'api',
    jenkins: 'gn-legacy-api',
    k8s: 'gn-legacy-api-internal',
    testAreas: [
      TEST_AREA.MM_AUTHENTICATION,
      TEST_AREA.MM_ADMIN_MANAGE_FACILITY_GROUPS,
      TEST_AREA.MM_ADMIN_MANAGE_SYSTEM_SETTINGS,
      TEST_AREA.MM_ACCOUNT_SETTINGS,
      TEST_AREA.MM_ELASTIC_SEARCH,
      TEST_AREA.MM_HOME,
    ],
  },
  'GolfNowEng/app': {
    github: 'app',
    jenkins: 'app',
    k8s: 'gn-legacy-app-internal',
    testAreas: [
      TEST_AREA.LM_GENERAL_CUSTOMERS,
      TEST_AREA.LM_GENERAL_RATES,
      TEST_AREA.LM_GENERAL_TAGS,
      TEST_AREA.LM_GENERAL_LOYALTY_PROGRAMS,
      TEST_AREA.LM_SETTINGS_COURSE_SETTINGS,
      TEST_AREA.LM_SETTINGS_BOOKING_ENGINE_SETTINGS,
      TEST_AREA.LM_MARKETING_PROMOTIONS_CURRENT_EXPIRED_DELETED,
      TEST_AREA.LM_MARKETING_PROMOTIONS_CAMPAIGN_MANAGEMENT,
      TEST_AREA.LM_MANAGE_MCO,
      TEST_AREA.LM_OTHER,
    ],
  },
  'GolfNowEng/be': {
    github: 'be',
    jenkins: 'be',
    k8s: 'gn-legacy-be-internal',
  },
  'GolfNowEng/worker': {
    github: 'worker',
    jenkins: 'gn-legacy-worker',
    k8s: 'gn-legacy-worker',
  },
  'GolfNowEng/hapi-sparkpost-webhook': {
    github: 'hapi-sparkpost-webhook',
    jenkins: 'hapi-sparkpost-webhook',
    k8s: 'hapi-sparkpost-webhook',
  },
  'GolfNowEng/phx-api-campaigns': {
    github: 'phx-api-campaigns',
    jenkins: 'phx-api-campaigns',
    k8s: 'phx-api-campaigns',
    testAreas: [
      TEST_AREA.MM_CAMPAIGNS_ONE_TIME_CAMPAIGNS,
      TEST_AREA.MM_CAMPAIGNS_RECURRING_CAMPAIGNS,
      TEST_AREA.MM_CAMPAIGNS_CAMPAIGN_ACTIONS,
    ],
  },
  'GolfNowEng/phx-api-channel': {
    github: 'phx-api-channel',
    jenkins: 'phx-api-channel',
    k8s: 'phx-api-channel',
    testAreas: [
      TEST_AREA.BE_USER_PROFILE,
      TEST_AREA.BE_API_2_1,
      TEST_AREA.BE_CHECKOUT_LOYALTY_CONFIRMATION,
    ],
  },
  'GolfNowEng/phx-api-credentials': {
    github: 'phx-api-credentials',
    jenkins: 'phx-api-credentials',
    k8s: 'phx-api-credentials',
    testAreas: [
      TEST_AREA.BE_USER_PROFILE,
    ],
  },
  'GolfNowEng/phx-api-customer-rollup': {
    github: 'phx-api-customer-rollup',
    jenkins: 'phx-api-customer-rollup',
    k8s: 'phx-api-customer-rollup',
  },
  'GolfNowEng/phx-api-files': {
    github: 'phx-api-files',
    jenkins: 'phx-api-files',
    k8s: 'phx-api-files',
  },
  'GolfNowEng/phx-api-g-1-cust-consumer': {
    github: 'phx-api-g-1-cust-consumer',
    jenkins: 'phx-api-g-1-cust-consumer',
    k8s: 'phx-api-g-1-cust-consumer',
  },
  'GolfNowEng/phx-api-gateway': {
    github: 'phx-api-gateway',
    jenkins: 'phx-api-gateway',
    k8s: 'phx-api-gateway',
    testAreas: [
      TEST_AREA.BE_USER_PROFILE,
      TEST_AREA.BE_CHECKOUT_LOYALTY_CONFIRMATION,
    ],
  },
  'GolfNowEng/phx-api-generic-cust-consumer': {
    github: 'phx-api-generic-cust-consumer',
    jenkins: 'phx-api-generic-cust-consumer',
    k8s: 'phx-api-generic-cust-consumer',
  },
  'GolfNowEng/phx-api-geoplaces-destinations-producer': {
    github: 'phx-api-geoplaces-destinations-producer',
    jenkins: 'phx-api-geoplaces-destinations-producer',
    k8s: 'phx-api-geoplaces-destinations-producer',
  },
  'GolfNowEng/phx-api-gn-rateset-consumer': {
    github: 'phx-api-gn-rateset-consumer',
    jenkins: 'phx-api-gn-rateset-consumer',
    k8s: 'phx-api-gn-rateset-consumer',
  },
  'GolfNowEng/phx-api-gn-reservation-consumer': {
    github: 'phx-api-gn-reservation-consumer',
    jenkins: 'phx-api-gn-reservation-consumer',
    k8s: 'phx-api-gn-reservation-consumer',
  },
  'GolfNowEng/phx-api-location': {
    github: 'phx-api-location',
    jenkins: 'phx-api-location',
    k8s: 'phx-api-location',
  },
  'GolfNowEng/phx-api-loyalty': {
    github: 'phx-api-loyalty',
    jenkins: 'phx-api-loyalty',
    k8s: 'phx-api-loyalty',
    testAreas: [
      TEST_AREA.BE_API_2_1,
      TEST_AREA.BE_CHECKOUT_LOYALTY_CONFIRMATION,
      TEST_AREA.LM_GENERAL_LOYALTY_PROGRAMS,
      TEST_AREA.MM_LOYALTY,
    ],
  },
  'GolfNowEng/phx-api-manage': {
    github: 'phx-api-manage',
    jenkins: 'phx-api-manage',
    k8s: 'phx-api-manage',
    testAreas: [
      TEST_AREA.MM_AUTHENTICATION,
      TEST_AREA.MM_ADMIN_MANAGE_FACILITY_GROUPS,
      TEST_AREA.MM_ADMIN_MANAGE_SYSTEM_SETTINGS,
      TEST_AREA.MM_ADMIN_MANAGE_SYSTEM_USERS,
      TEST_AREA.MM_ACCOUNT_SETTINGS,
      TEST_AREA.MM_ELASTIC_SEARCH,
      TEST_AREA.MM_HOME,
      TEST_AREA.MM_CAMPAIGNS_ONE_TIME_CAMPAIGNS,
      TEST_AREA.MM_CAMPAIGNS_RECURRING_CAMPAIGNS,
      TEST_AREA.MM_CAMPAIGNS_CAMPAIGN_ACTIONS,
      TEST_AREA.MM_FACILITY_SETTINGS_GENERAL,
      TEST_AREA.MM_FACILITY_SETTINGS_SOCIAL_MEDIA,
      TEST_AREA.MM_FACILITY_SETTINGS_MERGE_FIELDS,
      TEST_AREA.MM_FACILITY_SETTINGS_MEDIA_LIBRARY,
      TEST_AREA.MM_FACILITY_SETTINGS_MERGE_IMAGES,
      TEST_AREA.MM_FACILITY_SETTINGS_COURSE_APPS,
      TEST_AREA.MM_FACILITY_SETTINGS_COURSE_MAPPINGS,
      TEST_AREA.MM_LOYALTY,
      TEST_AREA.MM_CUSTOMERS_LIST,
      TEST_AREA.MM_CUSTOMERS_UPDATE,
      TEST_AREA.MM_CUSTOMERS_CREATE,
      TEST_AREA.MM_CUSTOMERS_TAGS,
      TEST_AREA.MM_CUSTOMERS_LISTS,
      TEST_AREA.MM_BE_SETTINGS_GENERAL,
      TEST_AREA.MM_BE_SETTINGS_INVENTORY,
      TEST_AREA.MM_BE_SETTINGS_RATES,
      TEST_AREA.MM_BE_SETTINGS_CHECKOUT,
      TEST_AREA.MM_BE_SETTINGS_APPEARANCE,
      TEST_AREA.MM_PROMOTIONS_UNIVERSAL,
      TEST_AREA.MM_PROMOTIONS_UNIQUE,
      TEST_AREA.MM_RESERVATION_CENTER_G1,
      TEST_AREA.MM_RESERVATION_CENTER_GNR,
    ],
  },
  'GolfNowEng/phx-api-organization': {
    github: 'phx-api-organization',
    jenkins: 'phx-api-organization',
    k8s: 'phx-api-organization',
    testAreas: [
      TEST_AREA.BE_API_2_1,
      TEST_AREA.BE_CHECKOUT_LOYALTY_CONFIRMATION,
      TEST_AREA.BE_MCO_GROUPS,
    ],
  },
  'GolfNowEng/phx-api-profile': {
    github: 'phx-api-profile',
    jenkins: 'phx-api-profile',
    k8s: 'phx-api-profile',
    testAreas: [
      TEST_AREA.BE_USER_PROFILE,
      TEST_AREA.BE_CHECKOUT_LOYALTY_CONFIRMATION,
      TEST_AREA.BE_RULES,
    ],
  },
  'GolfNowEng/phx-api-reservation-journal': {
    github: 'phx-api-reservation-journal',
    jenkins: 'phx-api-reservation-journal',
    k8s: 'phx-api-reservation-journal',
  },
  'GolfNowEng/phx-api-simple-webhook': {
    github: 'phx-api-simple-webhook',
    jenkins: 'phx-api-simple-webhook',
    k8s: 'phx-api-simple-webhook',
  },
  'GolfNowEng/phx-api-teetime-rules': {
    github: 'phx-api-teetime-rules',
    jenkins: 'phx-api-teetime-rules',
    k8s: 'phx-api-teetime-rules',
    testAreas: [
      TEST_AREA.BE_API_2_1,
      TEST_AREA.BE_CHECKOUT_LOYALTY_CONFIRMATION,
      TEST_AREA.BE_RULES,
    ],
  },
  'GolfNowEng/phx-api-teetime-search': {
    github: 'phx-api-teetime-search',
    jenkins: 'phx-api-teetime-search',
    k8s: 'phx-api-teetime-search',
    testAreas: [
      TEST_AREA.BE_API_2_1,
      TEST_AREA.BE_CHECKOUT_LOYALTY_CONFIRMATION,
      TEST_AREA.BE_RULES,
      TEST_AREA.BE_SHOPPING_CART,
      TEST_AREA.BE_FEATURED_PRODUCTS,
    ],
  },
  'GolfNowEng/phx-api-weather': {
    github: 'phx-api-weather',
    jenkins: 'phx-api-weather',
    k8s: 'phx-api-weather-internal',
  },
  'GolfNowEng/phx-consumer-facility-customer': {
    github: 'phx-consumer-facility-customer',
    jenkins: 'phx-consumer-facility-customer',
    k8s: 'phx-consumer-facility-customer',
  },
  'GolfNowEng/phx-consumer-legacy-facility': {
    github: 'phx-consumer-legacy-facility',
    jenkins: 'phx-consumer-legacy-facility',
    k8s: 'phx-consumer-legacy-facility',
  },
  'GolfNowEng/phx-consumer-organization': {
    github: 'phx-consumer-organization',
    jenkins: 'phx-consumer-organization',
    k8s: 'phx-consumer-organization',
  },
  'GolfNowEng/phx-consumer-redis-cache-manager': {
    github: 'phx-consumer-redis-cache-manager',
    jenkins: 'phx-consumer-redis-cache-manager',
    k8s: 'phx-consumer-redis-cache-manager',
  },
  'GolfNowEng/phx-consumer-twilio': {
    github: 'phx-consumer-twilio',
    jenkins: 'phx-consumer-twilio',
    k8s: 'phx-consumer-twilio',
  },
  'GolfNowEng/phx-ui-be': {
    github: 'phx-ui-be',
    jenkins: 'phx-ui-be',
    k8s: 'phx-ui-be-5-1',
    testAreas: [
      TEST_AREA.BE_USER_PROFILE,
      TEST_AREA.BE_API_2_1,
      TEST_AREA.BE_CHECKOUT_LOYALTY_CONFIRMATION,
      TEST_AREA.BE_RULES,
      TEST_AREA.BE_CHECK_IN,
      TEST_AREA.BE_INVITE_A_FRIEND,
      TEST_AREA.BE_SHOPPING_CART,
      TEST_AREA.BE_FEATURED_PRODUCTS,
      TEST_AREA.BE_G1_MEMBERSHIPS,
      TEST_AREA.BE_WALLET,
      TEST_AREA.BE_CANCELLATION,
      TEST_AREA.BE_MCO_GROUPS,
      TEST_AREA.BE_MODERN_MANAGE_SETTINGS,
    ],
  },
  'GolfNowEng/phx-api-be': {
    github: 'phx-api-be',
    jenkins: 'phx-api-be',
    k8s: 'phx-api-be-internal',
    testAreas: [
      TEST_AREA.BE_USER_PROFILE,
      TEST_AREA.BE_API_2_1,
      TEST_AREA.BE_CHECKOUT_LOYALTY_CONFIRMATION,
      TEST_AREA.BE_RULES,
      TEST_AREA.BE_CHECK_IN,
      TEST_AREA.BE_INVITE_A_FRIEND,
      TEST_AREA.BE_SHOPPING_CART,
      TEST_AREA.BE_FEATURED_PRODUCTS,
      TEST_AREA.BE_G1_MEMBERSHIPS,
      TEST_AREA.BE_WALLET,
      TEST_AREA.BE_CANCELLATION,
      TEST_AREA.BE_MCO_GROUPS,
      TEST_AREA.BE_MODERN_MANAGE_SETTINGS,
    ],
  },
  'GolfNowEng/phx-ui-groupon': {
    github: 'phx-ui-groupon',
    jenkins: 'phx-ui-groupon',
    k8s: 'phx-ui-groupon',
  },
  'GolfNowEng/phx-ui-loyalty-programs': {
    github: 'phx-ui-loyalty-programs',
    jenkins: 'phx-ui-loyalty-programs',
    k8s: 'phx-ui-loyalty-programs',
  },
  'GolfNowEng/phx-ui-manage': {
    github: 'phx-ui-manage',
    jenkins: 'phx-ui-manage',
    k8s: 'phx-ui-manage',
    testAreas: [
      TEST_AREA.MM_AUTHENTICATION,
      TEST_AREA.MM_ADMIN_MANAGE_FACILITY_GROUPS,
      TEST_AREA.MM_ADMIN_MANAGE_SYSTEM_SETTINGS,
      TEST_AREA.MM_ADMIN_MANAGE_SYSTEM_USERS,
      TEST_AREA.MM_ACCOUNT_SETTINGS,
      TEST_AREA.MM_ELASTIC_SEARCH,
      TEST_AREA.MM_HOME,
      TEST_AREA.MM_CAMPAIGNS_ONE_TIME_CAMPAIGNS,
      TEST_AREA.MM_CAMPAIGNS_RECURRING_CAMPAIGNS,
      TEST_AREA.MM_CAMPAIGNS_CAMPAIGN_ACTIONS,
      TEST_AREA.MM_FACILITY_SETTINGS_GENERAL,
      TEST_AREA.MM_FACILITY_SETTINGS_SOCIAL_MEDIA,
      TEST_AREA.MM_FACILITY_SETTINGS_MERGE_FIELDS,
      TEST_AREA.MM_FACILITY_SETTINGS_MEDIA_LIBRARY,
      TEST_AREA.MM_FACILITY_SETTINGS_MERGE_IMAGES,
      TEST_AREA.MM_FACILITY_SETTINGS_COURSE_APPS,
      TEST_AREA.MM_FACILITY_SETTINGS_COURSE_MAPPINGS,
      TEST_AREA.MM_LOYALTY,
      TEST_AREA.MM_CUSTOMERS_LIST,
      TEST_AREA.MM_CUSTOMERS_UPDATE,
      TEST_AREA.MM_CUSTOMERS_CREATE,
      TEST_AREA.MM_CUSTOMERS_TAGS,
      TEST_AREA.MM_CUSTOMERS_LISTS,
      TEST_AREA.MM_BE_SETTINGS_GENERAL,
      TEST_AREA.MM_BE_SETTINGS_INVENTORY,
      TEST_AREA.MM_BE_SETTINGS_RATES,
      TEST_AREA.MM_BE_SETTINGS_CHECKOUT,
      TEST_AREA.MM_BE_SETTINGS_APPEARANCE,
      TEST_AREA.MM_PROMOTIONS_UNIVERSAL,
      TEST_AREA.MM_PROMOTIONS_UNIQUE,
      TEST_AREA.MM_RESERVATION_CENTER_G1,
      TEST_AREA.MM_RESERVATION_CENTER_GNR,
    ],
  },
  'GolfNowEng/phx-ui-util-forms': {
    github: 'phx-ui-util-forms',
    jenkins: 'phx-ui-util-forms',
    k8s: 'phx-ui-util-forms',
  },
  'GolfNowEng/phx-api-message-webhook': {
    github: 'phx-api-message-webhook',
    jenkins: 'phx-api-message-webhook',
    k8s: 'phx-api-message-webhook-consumer',
  },
  'GolfNowEng/phx-api-reservation-messages': {
    github: 'phx-api-reservation-messages',
    jenkins: 'phx-api-reservation-messages',
    k8s: 'phx-api-reservation-messages',
  },
  'GolfNowEng/phx-api-third-party': {
    github: 'phx-api-third-party',
    jenkins: 'phx-api-third-party',
    k8s: 'phx-api-third-party',
    testAreas: [
      TEST_AREA.MM_RESERVATION_CENTER_G1,
    ],
  },
  'GolfNowEng/phx-api-payload-proxy': {
    github: 'phx-api-payload-proxy',
    jenkins: 'phx-api-payload-proxy',
    k8s: 'phx-api-payload-proxy',
  },
  'GolfNowEng/phx-api-crm': {
    github: 'phx-api-crm',
    jenkins: 'phx-api-crm',
    k8s: 'phx-api-crm-internal',
    testAreas: [
      TEST_AREA.MM_CUSTOMERS_LIST,
      TEST_AREA.MM_CUSTOMERS_UPDATE,
      TEST_AREA.MM_CUSTOMERS_CREATE,
      TEST_AREA.MM_CUSTOMERS_TAGS,
      TEST_AREA.MM_CUSTOMERS_LISTS,
    ],
  },
  'GolfNowEng/phx-api-customer-importer': {
    github: 'phx-api-customer-importer',
    jenkins: 'phx-api-customer-importer',
    k8s: 'phx-api-customer-importer-internal',
    testAreas: [
      TEST_AREA.MM_CUSTOMERS_IMPORT,
    ],
  },
  'GolfNowEng/phx-api-messaging': {
    github: 'phx-api-messaging',
    jenkins: 'phx-api-messaging',
    k8s: 'phx-api-messaging-internal',
  },
  'GolfNowEng/phx-api-reservation-center': {
    github: 'phx-api-reservation-center',
    jenkins: 'phx-api-reservation-center',
    k8s: 'phx-api-reservation-center-internal',
  },
  'GolfNowEng/phx-mf-be-admin': {
    github: 'phx-mf-be-admin',
    microFrontEnd: true,
  },
  'GolfNowEng/phx-api-account': {
    github: 'phx-api-account',
    jenkins: 'phx-api-account',
    k8s: 'phx-api-account',
  },
  'GolfNowEng/phx-api-forms': {
    github: 'phx-api-forms',
    jenkins: 'phx-api-forms',
    k8s: 'phx-api-forms-internal',
  },
  'GolfNowEng/phx-api-gnr-cust-consumer': {
    github: 'phx-api-gnr-cust-consumer',
    jenkins: 'phx-api-gnr-cust-consumer',
    k8s: 'phx-api-gnr-customer-consumer',
  },
  'GolfNowEng/phx-api-gn-customer-consumer': {
    github: 'phx-api-gn-customer-consumer',
    jenkins: 'phx-api-gn-customer-consumer',
    k8s: 'phx-api-gn-customer-consumer',
  },
  'GolfNowEng/phx-api-orders': {
    github: 'phx-api-orders',
    jenkins: 'phx-api-orders',
    k8s: 'phx-api-orders-internal',
  },
  'GolfNowEng/phx-ui-reservation-center': { skip: true },
  'GolfNowEng/phx-hapi-plugin-time-configs': { skip: true },
  'GolfNowEng/phx-dev-tools': { skip: true },
  'GolfNowEng/phx-hapi-plugin-customer-events': { skip: true },
  'GolfNowEng/phx-system-user-permissions': { skip: true },
  'GolfNowEng/phx-hapi-plugin-multi-mongo-connector': { skip: true },
  'GolfNowEng/phx-api-crm-v2': { skip: true },
  'GolfNowEng/phx-common': { skip: true },
  'GolfNowEng/phx-hapi-plugin-multi-elasticsearch-connector': { skip: true },
  'GolfNowEng/phx-dev-skaffold': { skip: true },
  'GolfNowEng/phx-mf-course-app-manage': { skip: true },
  'GolfNowEng/gn-api-inventory': {
    github: 'gn-api-inventory',
    jenkins: 'gn-api-inventory',
    k8s: 'gn-api-inventory',
  },
  'GolfNowEng/phx-mf-template': { skip: true },
  'GolfNowEng/phx-mf-design-system': { skip: true },
  'GolfNowEng/phx-mf-forms-manage': {
    github: 'phx-mf-forms-manage',
    microFrontEnd: true,
  },
  'GolfNowEng/phx-mf-reservation-center': {
    github: 'phx-mf-reservation-center',
    microFrontEnd: true,
  },
  'GolfNowEng/phx-api-marketing-account': {
    github: 'phx-api-marketing-account',
    jenkins: 'phx-api-marketing-account',
    k8s: 'phx-api-marketing-account-internal',
  },
  'GolfNowEng/phx-api-promo-codes': {
    github: 'phx-api-promo-codes',
    jenkins: 'phx-api-promo-codes',
    k8s: 'phx-api-promo-codes-internal',
  },
};
