'use strict';

const Async = require('async');
const FastCsv = require('fast-csv');
const Request = require('request');
const Q = require('q');

const csvPath = process.env.CSV_PATH || 'profile.csv';

const gateway = {
    baseUrl: process.env.GATEWAY || 'http://************:4311',
    headers: {
        'User-Agent': 'csv-to-profile',
        password: process.env.CHANNEL_PASSWORD,
        username: process.env.CHANNEL_USERNAME
    },
    json: true,
    method: 'GET'
};

const request = Request.defaults(gateway);

/*
    FILE FORMAT (including headers):
    entityType,tlFID,firstName,LastName,emailaddress

    1. Get the entity customer by email address and entityId
    2. "SmartPut" the entity customer with supplied data, if we did not get an entity customer
    3. Create profile using updated/current facility customer information
*/

const getEntityCustomer = (email, entityType, entityId) => {

    const deferred = Q.defer();

    const qs = {
        q: `primaryEmail:"${email}"`
    };

    if (entityType === 'facility') {
        qs.facilityIds = entityId;
    }
    else {
        qs.organizationIds = entityId;
    }

    // get the entity customer!
    const options = {
        method: 'GET',
        uri: '/service/gn-api-crm/customer',
        qs
    };

    request(options, (err, response, body) => {

        if (err){
            return deferred.reject(`Error getting customer: ${email}`);
        }

        if (response.statusCode !== 200){
            return deferred.reject(`${response.statusCode} getting ${email}`);
        }

        if ( body.items.length === 0) {
            return deferred.resolve(null);
        }

        return deferred.resolve(body.items[0]);
    });

    return deferred.promise;
};

const smartPutCustomer = (entityCustomer, csvCustomer, entityId) => {

    const deferred = Q.defer();

    // do we an entity customer?
    if (entityCustomer) {
        deferred.resolve(entityCustomer);
    }
    else {
        // "smartput" if we dont get an entity customer
        const options = {
            method: 'PUT',
            uri: `/service/gn-api-crm/${csvCustomer.entityType}/${entityId}/ext-customer`,
            qs: {
                mergeKey: 'email',
                mergeKeyValue: csvCustomer.email,
                source: 'profile-import-script',
                type: (csvCustomer.entityType === 'facility') ? 'CUST-FACILITY' : 'CUST-ORG'
            },
            body: {
                name: {
                    given: csvCustomer.firstName,
                    family: csvCustomer.lastName
                },
                emails: [{
                    primary: true,
                    value: csvCustomer.email
                }]
            },
            json: true
        };

        request(options, (err, response, body) => {

            if (err){
                return deferred.reject(`Problem creating: ${csvCustomer.email}`);
            }

            if (response.statusCode !== 200){
                return deferred.reject(`${response.statusCode} creating ${csvCustomer.email}`);
            }

            return deferred.resolve(body);
        });
    }

    return deferred.promise;
};

const createProfile = (entityCustomer, entityId, entityType, formatElastic) => {

    const deferred = Q.defer();

    let customer = {};

    if (formatElastic) {
        customer = {
            name: entityCustomer.nameExtended,
            emails: entityCustomer.emailsExtended,
            customerId: entityCustomer.customerId
        };

        if (entityType === 'facility'){
            customer.facilityId = entityId;
        }
        else {
            customer.organizationId = entityId;
        }
    }
    else {
        customer = entityCustomer;
    }

    const options = {
        method: 'POST',
        uri: `service/phx-api-profile/${entityType}/${entityId}/profile`,
        body: {
            type: 'basic',
            credentials: {
                username: entityCustomer.primaryEmail || entityCustomer.emails[0].value,
                password: 'Imp055ibleToGue55' // the peeps will need to login and reset their password
            },
            customer,
            sendEmail: false,
            source: 'csv-profile-import'
        },
        json: true
    };

    request(options, (err, response, body) => {

        if (err){
            console.log(err);
            return deferred.reject('Error creating profile');
        }

        if (response.statusCode === 409){
            return deferred.reject(`Profile already exists for ${options.body.credentials.username}`);
        }

        if (response.statusCode !== 200) {
            return deferred.reject(`Unable to create profile for ${options.body.credentials.username} ${response.statusCode} from ProfileAPI`);
        }

        return deferred.resolve(body);
    });

    return deferred.promise;
};

const queue = Async.queue((task, done) => {

    const locals = {
        csvCustomer: task,
        formatElastic: true,
        entity: {
            _id: task.entityId
        }
    };

    getEntityCustomer(locals.csvCustomer.email, locals.csvCustomer.entityType, locals.entity._id)
    .then((entityCustomer) => {

        locals.entityCustomer = entityCustomer;

        // flag for elastic customer formatting for profile creation
        if (entityCustomer === null){
            locals.formatElastic = false;
        }

        return smartPutCustomer(locals.entityCustomer, locals.csvCustomer, locals.entity._id);
    })
    .then((smartPuttedCustomer) => {

        // overwrite this guy incase we made a new entity customer
        locals.entityCustomer = smartPuttedCustomer;

        // make dat profile!
        return createProfile(locals.entityCustomer, locals.entity._id, locals.csvCustomer.entityType, locals.formatElastic);
    })
    .then((profile) => {

        console.log( require('util').inspect( `Profile created for ${locals.csvCustomer.email}`, { colors: true, depth: 50 } ) );
        done();
    })
    .catch((err) => {

        console.log( require('util').inspect( err, { colors: true, depth: 20 } ) );
        done();
    });
}, 100);

queue.drain = () => {

    console.log( require('util').inspect( 'All items are processed!', { colors: true, depth: 2 } ) );
};

const readCsv = () => {

    let parsed = false;

    FastCsv.fromPath(csvPath, { headers: true })
    .on('data', (data) => {

        parsed = true;
        // send it to a queue
        queue.push(data);
    })
    .on('end', (foo) => {

        if (parsed === false) {
            // we should never see this...should
            return new Error('unable to parse csv string');
        }
    })
    .on('error', (err) => {

        console.log( require('util').inspect( err, { colors: true, depth: 2 } ) );
    });
};

// vroom vroom vroom
readCsv();
