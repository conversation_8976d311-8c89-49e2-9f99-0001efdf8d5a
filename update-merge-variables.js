const axios = require('axios').default;
const cliProgress = require('cli-progress');

// The baseUrl depends on the environment, for api-messaging local as example
// http://localhost:3035/
const baseURL = 'https://qa-phx-api-messaging-east-1b.svc.golf/';
// for local dev the domain + /admin/tokens is used to retrieve a token
// http://dev-business.golfnowcentral.com:3000/admin/tokens
const token = 'token can be generated in modern manage';
const headers = { 'Authorization': `Bearer ${token}` };

const apiMessagingClient = axios.create({
    baseURL,
    headers
});

const variables = [
    /////////////////////// COURSE //////////////////////
    {
        displayName: 'Booking Engine',
        preview: 'https://golfnow-u-links.book.teeitup.com',
        category: 'COURSE',
        var: 'BOOKING_ENGINE',
        description: 'The booking engine for the course',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'profileCreated',
            'profileVerification',
            'forgotPassword',
            'creditCardExpire',
            'creditCardDeclined',
            'membershipPurchaseConfirmation',
            'giftCertificatePurchaseConfirmation',
            'giftCertificateRecipient',
            'campaign',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms',
            'teesheetMessage',
            'creditAccountBilling'
        ]
    },
    {
        displayName: 'Course Address',
        preview: '7580 Golf Channel Drive Orlando, FL 32819',
        category: 'COURSE',
        var: 'COURSE_ADDRESS',
        description: 'The address of the course',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'profileCreated',
            'profileVerification',
            'forgotPassword',
            'creditCardExpire',
            'creditCardDeclined',
            'membershipPurchaseConfirmation',
            'giftCertificatePurchaseConfirmation',
            'giftCertificateRecipient',
            'campaign',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms',
            'teesheetMessage',
            'creditAccountBilling'
        ]
    },
    {
        displayName: 'Course City',
        preview: 'Orlando',
        category: 'COURSE',
        var: 'COURSE_CITY',
        description: "The course's city",
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'profileCreated',
            'profileVerification',
            'forgotPassword',
            'creditCardExpire',
            'creditCardDeclined',
            'membershipPurchaseConfirmation',
            'giftCertificatePurchaseConfirmation',
            'giftCertificateRecipient',
            'campaign',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms',
            'teesheetMessage',
            'creditAccountBilling'
        ]
    },
    {
        displayName: 'Course Name',
        preview: 'GolfNow Links',
        category: 'COURSE',
        var: 'COURSE_NAME',
        description: 'The name of the course',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'profileCreated',
            'profileVerification',
            'forgotPassword',
            'creditCardExpire',
            'creditCardDeclined',
            'membershipPurchaseConfirmation',
            'giftCertificatePurchaseConfirmation',
            'giftCertificateRecipient',
            'campaign',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms',
            'teesheetMessage',
            'creditAccountBilling'
        ]
    },
    {
        displayName: 'Course Phone',
        preview: '**********',
        category: 'COURSE',
        var: 'COURSE_PHONE',
        description: 'The phone number for the course',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'profileCreated',
            'profileVerification',
            'forgotPassword',
            'creditCardExpire',
            'creditCardDeclined',
            'membershipPurchaseConfirmation',
            'giftCertificatePurchaseConfirmation',
            'giftCertificateRecipient',
            'campaign',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms',
            'teesheetMessage',
            'creditAccountBilling'
        ]
    },
    {
        displayName: 'Course Website',
        preview: 'http://golfnowlinks.com',
        category: 'COURSE',
        var: 'COURSE_URL',
        description: 'The website for the course',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'profileCreated',
            'profileVerification',
            'forgotPassword',
            'creditCardExpire',
            'creditCardDeclined',
            'membershipPurchaseConfirmation',
            'giftCertificatePurchaseConfirmation',
            'giftCertificateRecipient',
            'campaign',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms',
            'teesheetMessage',
            'creditAccountBilling'
        ]
    },
    {
        displayName: 'Golf Advisor',
        preview: 'http://www.golfpass.com/travel-advisor/courses/1234-golfnow-links',
        category: 'COURSE',
        var: 'COURSE_GOLFADVISOR',
        description: "The course's golf advisor url",
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'profileCreated',
            'profileVerification',
            'forgotPassword',
            'creditCardExpire',
            'creditCardDeclined',
            'membershipPurchaseConfirmation',
            'giftCertificatePurchaseConfirmation',
            'giftCertificateRecipient',
            'campaign',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms',
            'teesheetMessage',
            'creditAccountBilling'
        ]
    },
    {
        displayName: "It's On Me",
        preview: 'http://www.itson.me/widget/golfnow_links_menu#/merchant/123',
        category: 'COURSE',
        var: 'COURSE_ITSONME',
        description: "The course's It's On Me url",
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'profileCreated',
            'profileVerification',
            'forgotPassword',
            'creditCardExpire',
            'creditCardDeclined',
            'membershipPurchaseConfirmation',
            'giftCertificatePurchaseConfirmation',
            'giftCertificateRecipient',
            'campaign',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms',
            'teesheetMessage',
            'creditAccountBilling'
        ]
    },
    {
        displayName: 'Course Logo',
        preview: '',
        category: 'COURSE',
        var: 'COURSE_LOGO',
        description: 'The logo for this course',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'profileCreated',
            'profileVerification',
            'forgotPassword',
            'creditCardExpire',
            'creditCardDeclined',
            'membershipPurchaseConfirmation',
            'giftCertificatePurchaseConfirmation',
            'giftCertificateRecipient',
            'campaign',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'teesheetMessage',
            'creditAccountBilling'
        ]
    },
    {
        displayName: 'Course Image 1',
        preview: '',
        category: 'COURSE',
        var: 'COURSE_IMAGE1',
        description: '',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'profileCreated',
            'profileVerification',
            'forgotPassword',
            'creditCardExpire',
            'creditCardDeclined',
            'membershipPurchaseConfirmation',
            'giftCertificatePurchaseConfirmation',
            'giftCertificateRecipient',
            'campaign',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'teesheetMessage',
            'creditAccountBilling'
        ]
    },
    {
        displayName: 'Course Image 2',
        preview: '',
        category: 'COURSE',
        var: 'COURSE_IMAGE2',
        description: '',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'profileCreated',
            'profileVerification',
            'forgotPassword',
            'creditCardExpire',
            'creditCardDeclined',
            'membershipPurchaseConfirmation',
            'giftCertificatePurchaseConfirmation',
            'giftCertificateRecipient',
            'campaign',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'teesheetMessage',
            'creditAccountBilling'
        ]
    },
    {
        displayName: 'Course Image 3',
        preview: '',
        category: 'COURSE',
        var: 'COURSE_IMAGE3',
        description: '',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'profileCreated',
            'profileVerification',
            'forgotPassword',
            'creditCardExpire',
            'creditCardDeclined',
            'membershipPurchaseConfirmation',
            'giftCertificatePurchaseConfirmation',
            'giftCertificateRecipient',
            'campaign',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'teesheetMessage',
            'creditAccountBilling'
        ]
    },
    {
        displayName: 'Course Image 4',
        preview: '',
        category: 'COURSE',
        var: 'COURSE_IMAGE4',
        description: '',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'profileCreated',
            'profileVerification',
            'forgotPassword',
            'creditCardExpire',
            'creditCardDeclined',
            'membershipPurchaseConfirmation',
            'giftCertificatePurchaseConfirmation',
            'giftCertificateRecipient',
            'campaign',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'teesheetMessage',
            'creditAccountBilling'
        ]
    },
    {
        displayName: 'Course Image 5',
        preview: '',
        category: 'COURSE',
        var: 'COURSE_IMAGE5',
        description: '',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'profileCreated',
            'profileVerification',
            'forgotPassword',
            'creditCardExpire',
            'creditCardDeclined',
            'membershipPurchaseConfirmation',
            'giftCertificatePurchaseConfirmation',
            'giftCertificateRecipient',
            'campaign',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'teesheetMessage',
            'creditAccountBilling'
        ]
    },
    {
        displayName: 'Twitter URL',
        preview: 'http://www.twitter.com/golfnowlinks',
        category: 'COURSE',
        var: 'TWITTER_URL',
        description: "The course's Twitter page",
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'profileCreated',
            'profileVerification',
            'forgotPassword',
            'creditCardExpire',
            'creditCardDeclined',
            'membershipPurchaseConfirmation',
            'giftCertificatePurchaseConfirmation',
            'giftCertificateRecipient',
            'campaign',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms',
            'teesheetMessage',
            'creditAccountBilling'
        ]
    },
    {
        displayName: 'Twitter Handle',
        preview: '@GolfNowLinks',
        category: 'COURSE',
        var: 'TWITTER_HANDLE',
        description: "The course's Twitter handle",
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'profileCreated',
            'profileVerification',
            'forgotPassword',
            'creditCardExpire',
            'creditCardDeclined',
            'membershipPurchaseConfirmation',
            'giftCertificatePurchaseConfirmation',
            'giftCertificateRecipient',
            'campaign',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms',
            'teesheetMessage',
            'creditAccountBilling'
        ]
    },
    {
        displayName: 'Instagram URL',
        preview: 'http://www.instagram.com/golfnowlinks',
        category: 'COURSE',
        var: 'INSTAGRAM_URL',
        description: "The course's Instagram URL",
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'profileCreated',
            'profileVerification',
            'forgotPassword',
            'creditCardExpire',
            'creditCardDeclined',
            'membershipPurchaseConfirmation',
            'giftCertificatePurchaseConfirmation',
            'giftCertificateRecipient',
            'campaign',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms',
            'teesheetMessage',
            'creditAccountBilling'
        ]
    },
    {
        displayName: 'Instagram Handle',
        preview: '@GolfNowLinks',
        category: 'COURSE',
        var: 'INSTAGRAM_HANDLE',
        description: "The course's Instagram Handle",
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'profileCreated',
            'profileVerification',
            'forgotPassword',
            'creditCardExpire',
            'creditCardDeclined',
            'membershipPurchaseConfirmation',
            'giftCertificatePurchaseConfirmation',
            'giftCertificateRecipient',
            'campaign',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms',
            'teesheetMessage',
            'creditAccountBilling'
        ]
    },
    {
        displayName: 'Facebook URL',
        preview: 'http://www.facebook.com/golfnowlinks',
        category: 'COURSE',
        var: 'FACEBOOK_URL',
        description: "The course's Facebook page",
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'profileCreated',
            'profileVerification',
            'forgotPassword',
            'creditCardExpire',
            'creditCardDeclined',
            'membershipPurchaseConfirmation',
            'giftCertificatePurchaseConfirmation',
            'giftCertificateRecipient',
            'campaign',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms',
            'teesheetMessage',
            'creditAccountBilling'
        ]
    },
    {
        displayName: 'Facebook Handle',
        preview: '@GolfNowLinks',
        category: 'COURSE',
        var: 'FACEBOOK_HANDLE',
        description: "The course's Facebook handle",
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'profileCreated',
            'profileVerification',
            'forgotPassword',
            'creditCardExpire',
            'creditCardDeclined',
            'membershipPurchaseConfirmation',
            'giftCertificatePurchaseConfirmation',
            'giftCertificateRecipient',
            'campaign',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms',
            'teesheetMessage',
            'creditAccountBilling'
        ]
    },
    {
        displayName: 'Course Tee Time Policy',
        preview:
            'Our cancellation policy requires 24-hour notice. Not showing or paying for this reservation in full will result in your credit card being charged for the remaining fees due.',
        category: 'COURSE',
        var: 'COURSE_TEETIME_POLICY',
        description: '',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'profileCreated',
            'profileVerification',
            'forgotPassword',
            'creditCardExpire',
            'creditCardDeclined',
            'membershipPurchaseConfirmation',
            'giftCertificatePurchaseConfirmation',
            'giftCertificateRecipient',
            'campaign',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms',
            'teesheetMessage',
            'creditAccountBilling'
        ]
    },
    {
        displayName: 'Checkin Instructions',
        preview: 'Checkin Instructions',
        category: 'COURSE',
        var: 'CHECKIN_INSTRUCTIONS',
        description: 'Checkin instructions',
        tags: [
            'sms',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'teesheetMessage'
        ]
    },
    /////////////////////// RESERVATION //////////////////////
    {
        displayName: 'Confirmation Number',
        preview: '*********',
        category: 'RESERVATION',
        var: 'RESERVATION_CONFIRMATION_NUMBER',
        description: '',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms'
        ]
    },
    {
        displayName: 'Confirmation Page',
        preview: 'https://kissimmee-oaks-gc-demo.book.teeitup.com/reservation/history',
        category: 'RESERVATION',
        var: 'RESERVATION_SHORT_URL',
        description: '',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms'
        ]
    },
    {
        displayName: 'Reservation Notes',
        preview: 'I need left handed clubs.',
        category: 'RESERVATION',
        var: 'RESERVATION_NOTES',
        description: '',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms'
        ]
    },
    {
        displayName: 'Number of Players',
        preview: '3',
        category: 'RESERVATION',
        var: 'RESERVATION_TOTALPLAYERS',
        description: '',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms'
        ]
    },
    {
        displayName: 'Reservation Name',
        preview: 'John Smith',
        category: 'RESERVATION',
        var: 'RESERVATION_NAME',
        description: 'Customer name on a booked reservation',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms'
        ]
    },
    {
        displayName: 'Reservation Date (Day of week, month, day, & year)',
        preview: 'Wednesday, July 27, 2022',
        category: 'RESERVATION',
        var: 'RESERVATION_DATE_FULL',
        description: '',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms'
        ]
    },
    {
        displayName: 'Reservation Day (Number)',
        preview: '27th',
        category: 'RESERVATION',
        var: 'RESERVATION_DATE_DAY_ORDINAL',
        description: '',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms'
        ]
    },
    {
        displayName: 'Reservation Date (Day of week, month, & day)',
        preview: 'Thursday, October 13',
        category: 'RESERVATION',
        var: 'RESERVATION_DATE_SHORT',
        description: '',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms'
        ]
    },
    {
        displayName: 'Reservation Time',
        preview: '3:26 pm',
        category: 'RESERVATION',
        var: 'RESERVATION_TEETIMES_JOINED',
        description: '',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms'
        ]
    },
    {
        displayName: 'Total',
        preview: '$20.00',
        category: 'RESERVATION',
        var: 'RESERVATION_FEES_TOTAL',
        description: '',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms'
        ]
    },
    {
        displayName: 'Online Fees',
        preview: '$20.00',
        category: 'RESERVATION',
        var: 'RESERVATION_FEES_TRANSACTION',
        description: '',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms'
        ]
    },
    {
        displayName: 'Green Fees',
        preview: '$20.00',
        category: 'RESERVATION',
        var: 'RESERVATION_FEES_GREEN',
        description: '',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms'
        ]
    },
    {
        displayName: 'Paid Online',
        preview: '$20.00',
        category: 'RESERVATION',
        var: 'RESERVATION_FEES_ONLINE',
        description: '',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms'
        ]
    },
    {
        displayName: 'Total Due at Course',
        preview: '$20.00',
        category: 'RESERVATION',
        var: 'RESERVATION_FEES_COURSE',
        description: '',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms'
        ]
    },
    {
        displayName: 'Fees Per Player',
        preview: '$20.00',
        category: 'RESERVATION',
        var: 'RESERVATION_FEES_PER_PLAYER',
        description: '',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms'
        ]
    },
    {
        displayName: 'Sales Tax',
        preview: '$20.00',
        category: 'RESERVATION',
        var: 'RESERVATION_FEES_SALES_TAX',
        description: '',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms'
        ]
    },
    {
        displayName: 'Rate Name',
        preview: 'Member',
        category: 'RESERVATION',
        var: 'RESERVATION_INVOICE_RATENAME',
        description: '',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms'
        ]
    },
    {
        displayName: 'Tee Time Notes',
        preview: 'TAX NOT INCLUDED.',
        category: 'RESERVATION',
        var: 'RESERVATION_INVOICE_TEETIMENOTES',
        description: '',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms'
        ]
    },
    {
        displayName: 'Tee Time Policy',
        preview:
            'Our cancellation policy requires 24-hour notice. Not showing or paying for this reservation in full will result in your credit card being charged for the remaining fees due.',
        category: 'RESERVATION',
        var: 'RESERVATION_INVOICE_TERMSANDCONDITIONS',
        description: '',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms'
        ]
    },
    {
        displayName: 'Hole Count',
        preview: '18',
        category: 'RESERVATION',
        var: 'RESERVATION_INVOICE_HOLECOUNT',
        description: '',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms'
        ]
    },
    {
        displayName: 'G1 Check-in',
        preview: 'https://golfnow-u-links.book.teeitup.com/reservation/history?reservationId=',
        category: 'RESERVATION',
        var: 'G1_CHECKIN',
        description: '',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms'
        ]
    },
    {
        displayName: 'BRS Course Name',
        preview: 'GolfNow Links',
        category: 'RESERVATION',
        var: 'BRS_COURSE_NAME',
        description: 'Name of course for booked reservation',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms'
        ]
    },
    {
        displayName: 'Players Verbiage',
        preview: '2 Players',
        category: 'RESERVATION',
        var: 'RESERVATION_PLAYERS_VERBIAGE',
        description: 'Number of players for booked reservation',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms'
        ]
    },
    {
        displayName: 'Reservation Date (Day of week & month)',
        preview: 'Thursday, October',
        category: 'RESERVATION',
        var: 'RESERVATION_DATE_MONTH_SHORT',
        description: 'Week day and month of the reservation',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms'
        ]
    },
    ////////////////////////// CUSTOMER /////////////////////////
    {
        displayName: 'First Name',
        preview: 'John',
        category: 'GOLFER',
        var: 'USER_FIRSTNAME',
        description: 'The first name of the golfer',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'campaign',
            'membershipPurchaseConfirmation',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms'
        ]
    },
    {
        displayName: 'Full Name',
        preview: 'John Smith',
        category: 'GOLFER',
        var: 'USER_FULLNAME',
        description: 'The full name of the golfer',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'campaign',
            'membershipPurchaseConfirmation',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms'
        ]
    },
    {
        displayName: 'Last Name',
        preview: 'Smith',
        category: 'GOLFER',
        var: 'USER_SURNAME',
        description: 'The last name of the golfer',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'campaign',
            'membershipPurchaseConfirmation',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms'
        ]
    },
    {
        displayName: 'Email Address',
        preview: '<EMAIL>',
        category: 'GOLFER',
        var: 'USER_EMAIL',
        description: '',
        tags: [
            'confirmation',
            'cancellation',
            'reminder',
            'thankYou',
            'reservationChange',
            'campaign',
            'membershipPurchaseConfirmation',
            'checkinAndPay',
            'checkinAndPayConfirmation',
            'sms'
        ]
    },
    ///////////////////////////G1 MESSAGES//////////////////////////
    {
        displayName: 'Last 4 of Credit Card',
        preview: '1234',
        category: 'G1_ORDER',
        var: 'LAST4OF_CC',
        description: '',
        tags: ['giftCertificatePurchaseConfirmation', 'giftCertificateRecipient', 'membershipPurchaseConfirmation']
    },
    {
        displayName: 'Location Name',
        preview: 'Golfnow Links',
        category: 'G1_ORDER',
        var: 'LOCATION_NAME',
        description: '',
        tags: ['giftCertificatePurchaseConfirmation', 'giftCertificateRecipient', 'membershipPurchaseConfirmation']
    },
    {
        displayName: 'Brand Name',
        preview: 'GolfNow Golf Partners',
        category: 'G1_ORDER',
        var: 'BRAND_NAME',
        description: '',
        tags: ['giftCertificatePurchaseConfirmation', 'giftCertificateRecipient', 'membershipPurchaseConfirmation']
    },
    {
        displayName: 'Gift Message',
        preview: 'Happy birthday, John!',
        category: 'G1_ORDER',
        var: 'GIFT_MESSAGE',
        description: '',
        tags: ['giftCertificatePurchaseConfirmation', 'giftCertificateRecipient']
    },
    {
        displayName: 'Purchaser Email Address',
        preview: '<EMAIL>',
        category: 'G1_ORDER',
        var: 'PURCHASER_EMAIL',
        description: '',
        tags: ['giftCertificatePurchaseConfirmation', 'giftCertificateRecipient']
    },
    {
        displayName: 'Purchaser Full Name',
        preview: 'John Smith',
        category: 'G1_ORDER',
        var: 'PURCHASER_NAME',
        description: '',
        tags: ['giftCertificatePurchaseConfirmation', 'giftCertificateRecipient']
    },
    {
        displayName: 'Recipient Email Address',
        preview: '<EMAIL>',
        category: 'G1_ORDER',
        var: 'RECIPIENT_EMAIL',
        description: '',
        tags: ['giftCertificatePurchaseConfirmation', 'giftCertificateRecipient']
    },
    {
        displayName: 'Recipient Full Name',
        preview: 'John Smith',
        category: 'G1_ORDER',
        var: 'RECIPIENT_NAME',
        description: '',
        tags: ['giftCertificatePurchaseConfirmation', 'giftCertificateRecipient']
    },
    {
        displayName: 'Delivery Date',
        preview: 'Wednesday, July 27, 2022',
        category: 'G1_ORDER',
        var: 'DELIVERY_DATE',
        description: '',
        tags: ['giftCertificatePurchaseConfirmation', 'giftCertificateRecipient']
    },
    {
        displayName: 'Date of Purchase',
        preview: 'Wednesday, July 27, 2022',
        category: 'G1_ORDER',
        var: 'DATEOF_PURCHASE',
        description: '',
        tags: ['giftCertificatePurchaseConfirmation', 'giftCertificateRecipient', 'membershipPurchaseConfirmation']
    },
    {
        displayName: 'Order Number',
        preview: '**********01',
        category: 'G1_ORDER',
        var: 'ORDER_NUMBER',
        description: '',
        tags: ['giftCertificatePurchaseConfirmation', 'giftCertificateRecipient']
    },
    {
        displayName: 'Gift Certificate Number',
        preview: '**********012',
        category: 'G1_ORDER',
        var: 'GIFT_CERTIFICATE_NUMBER',
        description: '',
        tags: ['giftCertificatePurchaseConfirmation', 'giftCertificateRecipient']
    },
    {
        displayName: 'Gift Certificate Amount',
        preview: '$100.00',
        category: 'G1_ORDER',
        var: 'GIFT_CERTIFICATE_AMOUNT',
        description: '',
        tags: ['giftCertificatePurchaseConfirmation', 'giftCertificateRecipient']
    },
    {
        displayName: 'Gift Certificate Expiration Date',
        preview: 'Wednesday, July 27, 2022',
        category: 'G1_ORDER',
        var: 'GIFT_CERTIFICATE_EXPIRATION_DATE',
        description: '',
        tags: ['giftCertificatePurchaseConfirmation', 'giftCertificateRecipient']
    },
    {
        displayName: 'Gift Certificate Program',
        preview: 'GolfNow Links',
        category: 'G1_ORDER',
        var: 'GIFT_CERTIFICATE_PROGRAM',
        description: '',
        tags: ['giftCertificatePurchaseConfirmation', 'giftCertificateRecipient']
    },
    {
        displayName: 'Charged Amount',
        preview: '$100.00',
        category: 'G1_ORDER',
        var: 'CHARGED_AMOUNT',
        description: '',
        tags: ['giftCertificatePurchaseConfirmation', 'giftCertificateRecipient', 'membershipPurchaseConfirmation']
    },
    {
        displayName: 'Barcode',
        preview: 'Barcode image',
        category: 'G1_ORDER',
        var: 'BAR_CODE',
        description: '',
        tags: ['giftCertificatePurchaseConfirmation', 'giftCertificateRecipient']
    },
    {
        displayName: 'From Name',
        preview: 'John Smith',
        category: 'G1_ORDER',
        var: 'FROM_NAME',
        description: '',
        tags: ['giftCertificatePurchaseConfirmation', 'giftCertificateRecipient', 'membershipPurchaseConfirmation']
    },
    {
        displayName: 'From Email',
        preview: '<EMAIL>',
        category: 'G1_ORDER',
        var: 'FROM_EMAIL',
        description: '',
        tags: ['giftCertificatePurchaseConfirmation', 'giftCertificateRecipient', 'membershipPurchaseConfirmation']
    },
    {
        displayName: 'Monthly Price',
        preview: '$82.50',
        category: 'G1_ORDER',
        var: 'MONTHLY_PRICE',
        description: '',
        tags: ['membershipPurchaseConfirmation']
    },
    {
        displayName: 'Billing Frequency',
        preview: 'Monthly',
        category: 'G1_ORDER',
        var: 'BILLING_FREQUENCY',
        description: '',
        tags: ['membershipPurchaseConfirmation']
    },
    {
        displayName: 'Day to Charge',
        preview: '5th',
        category: 'G1_ORDER',
        var: 'DAY_TO_CHARGE',
        description: '',
        tags: ['membershipPurchaseConfirmation']
    },
    {
        displayName: 'Membership Plan Name',
        preview: 'Annual Membership',
        category: 'G1_ORDER',
        var: 'MEMBERSHIP_PLAN_NAME',
        description: '',
        tags: ['membershipPurchaseConfirmation']
    },
    {
        displayName: 'Membership Level Name',
        preview: 'Golf',
        category: 'G1_ORDER',
        var: 'MEMBERSHIP_LEVEL_NAME',
        description: '',
        tags: ['membershipPurchaseConfirmation']
    },
    {
        displayName: 'Member Card Number',
        preview: '112233445',
        category: 'G1_ORDER',
        var: 'MEMBERSHIP_CARD_NUMBER',
        description: '',
        tags: ['membershipPurchaseConfirmation']
    },
    {
        displayName: 'Plan Start Date',
        preview: 'Monday, October 31, 2022',
        category: 'G1_ORDER',
        var: 'START_DATE',
        description: '',
        tags: ['membershipPurchaseConfirmation']
    },
    {
        displayName: 'Plan End Date',
        preview: 'Tuesday, October 31, 2023',
        category: 'G1_ORDER',
        var: 'END_DATE',
        description: '',
        tags: ['membershipPurchaseConfirmation']
    },
    {
        displayName: 'Initiation Fee',
        preview: '$82.50',
        category: 'G1_ORDER',
        var: 'INITIATION_FEE',
        description: '',
        tags: ['membershipPurchaseConfirmation']
    },
    {
        displayName: 'Order Notes',
        preview: 'Interested in referral benefits!',
        category: 'G1_ORDER',
        var: 'NOTES',
        description: '',
        tags: ['membershipPurchaseConfirmation']
    },
    {
        displayName: 'Employee Name',
        preview: 'John Smith',
        category: 'G1_ORDER',
        var: 'EMPLOYEE_NAME',
        description: '',
        tags: ['membershipPurchaseConfirmation']
    },
    {
        displayName: 'Benefits',
        preview: 'New Benefit (1 available)',
        category: 'G1_ORDER',
        var: 'BENEFITS',
        description: '',
        tags: ['membershipPurchaseConfirmation']
    },
    /////////////////////////// PROMOTION //////////////////////////
    {
        displayName: 'Promotion Code',
        preview: 'SAVE20',
        category: 'PROMOTION',
        var: 'PROMOTION_CODE',
        description: 'The promotion code',
        tags: ['campaign']
    },
    {
        displayName: 'Promotion End Date',
        preview: 'Sunday, July 31, 2022',
        category: 'PROMOTION',
        var: 'PROMOTION_DATETO_FORMATTED_DATE_FULL',
        description: 'The promotion code end date',
        tags: ['campaign']
    },
    {
        displayName: 'Promotion End Time',
        preview: '11:59 pm',
        category: 'PROMOTION',
        var: 'PROMOTION_DATETO_FORMATTED_TIME_USA',
        description: 'The promotion code end time',
        tags: ['campaign']
    },
    {
        displayName: 'Promotion Start Date',
        preview: 'Friday, July 1, 2022',
        category: 'PROMOTION',
        var: 'PROMOTION_DATEFROM_FORMATTED_DATE_FULL',
        description: 'The promotion code start date',
        tags: ['campaign']
    },
    {
        displayName: 'Promotion Start Time',
        preview: '12:01 am',
        category: 'PROMOTION',
        var: 'PROMOTION_DATEFROM_FORMATTED_TIME_USA',
        description: 'The promotion code start time',
        tags: ['campaign']
    },
    {
        displayName: 'Promotion Discount',
        preview: '20%',
        category: 'PROMOTION',
        var: 'PROMOTION_DISCOUNT_FORMATTED',
        description: 'The promotion code discount',
        tags: ['campaign']
    },
    /////////////////////////// TEESHEET MESSAGES //////////////////////////
    {
        displayName: 'Body HTML',
        preview: 'Body HTML',
        category: 'BODY_HTML',
        var: 'BODY_HTML',
        description: 'external HTML',
        tags: ['teesheetMessage', 'creditAccountBilling']
    },
];

const deleteMergeVariables = async (ids, currentMergeVars) => {
    let failed = 0;

    const deleteProgressBar = new cliProgress.SingleBar(
        {
            format: 'Delete Merge Vars [{bar}] {percentage}% | ETA: {eta}s | {value}/{total}'
        },
        cliProgress.Presets.shades_classic
    );
    deleteProgressBar.start(currentMergeVars.length, 0);

    for (let i = 0; i < ids.length; i++) {
        try {
            await apiMessagingClient.delete(`/v1/merge-vars/${ids[i]}`);
        } catch (error) {
            ++failed;
        } finally {
            deleteProgressBar.increment();
        }
    }

    deleteProgressBar.stop();

    if (failed) {
        console.log(`Merge vars failed to delete: ${failed}`);
    }
};

const createMergeVariables = async (payload) => {
    let failed = 0;

    const createProgressBar = new cliProgress.SingleBar(
        {
            format: 'Create Merge Vars [{bar}] {percentage}% | ETA: {eta}s | {value}/{total} | Variable: {variable}'
        },
        cliProgress.Presets.shades_classic
    );

    createProgressBar.start(payload.length, 0);

    for (let i = 0; i < payload.length; i++) {
        try {
            await apiMessagingClient.post('/v1/merge-vars', payload[i]);
        } catch (error) {
            ++failed;
        } finally {
            createProgressBar.increment(1, { variable: payload[i].displayName });
        }
    }

    createProgressBar.stop();

    if (failed) {
        console.log(`Merge vars failed to create: ${failed}`);
    }
};

const listExistingMergeVars = async () => {
    try {
        const { data } = await apiMessagingClient.get('/v1/merge-vars');

        return data;
    } catch (error) {
        console.error(error);

        process.exit(1);
    }
};

(async () => {
    const curentMergeVars = await listExistingMergeVars();
    const mergeVarsIds = curentMergeVars.map((mergeVar) => mergeVar._id);

    await deleteMergeVariables(mergeVarsIds, curentMergeVars);
    await createMergeVariables(variables);
})();
