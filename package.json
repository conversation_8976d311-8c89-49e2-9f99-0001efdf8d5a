{"name": "phx-dev-tools", "version": "1.0.0", "description": "Development tools and utility scripts for Phoenix projects", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {"amqplib": "^0.6.0", "async": "^3.2.0", "aws-sdk": "^2.1691.0", "axios": "^0.21.4", "bulk-mongo": "^0.1.0", "chalk": "^4.1.0", "cli-progress": "^3.11.2", "dotenv": "^16.4.5", "fast-csv": "^4.3.2", "highland": "^3.0.0-beta.3", "JSONStream": "^1.3.5", "lodash": "^4.17.20", "mime-types": "^2.1.35", "minimist": "^1.2.5", "mongodb": "^3.6.2", "q": "^1.5.1", "rabbit.js": "^0.4.4", "ramda": "^0.27.1", "request": "^2.88.2", "split": "^1.0.1", "uglify-js": "^3.11.0"}, "devDependencies": {"@hapi/eslint-config-hapi": "^13.0.2", "@hapi/eslint-plugin-hapi": "^4.3.6", "eslint": "^7.10.0"}}