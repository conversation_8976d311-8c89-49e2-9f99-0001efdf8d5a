// First run `npm install mongodb`

// ###############################################################

// This is an example of an aggregate query used to create a new temp table. Might come in handy in the future.
//
// db.getCollection('uniques').aggregate(
//     { $group: { 
//         _id: { promotionId: "$promotionId", customerId: "$customerId", facilityId: "$facilityId" },
//         count: { $sum:  1 },
//         docs: { $push: "$_id" }
//     }},
//     { $match: {
//         count: { $gt : 1 }
//     }},
//     {
//         "$out": "update-collection-temp"
//     }
// );

// ###############################################################

var MongoClient = require('mongodb').MongoClient;
var connectionEndpoint = process.env.MONGO_URI || 'mongodb://192.168.99.100:27017/database';
var dataCollectionName = process.env.DATA_COLLECTION_NAME || 'update-collection-temp';
var updateCollectionName = process.env.UPDATE_COLLECTION_NAME || 'update-collection';

MongoClient.connect(connectionEndpoint, function(err, db) {

    if (err) { 
        console.log('Error connecting to database.');
        return console.log(err); 
    }

    // Toggle logging with this variable
    var showLogging = true;
    var dataCollection = db.collection(dataCollectionName);
    var updateCollection = db.collection(updateCollectionName);

    // Get all the results from our temp table
    dataCollection.find({}).toArray(function(err, docs) {

        if (err) {
            console.log('Find Error');
            console.log(err);
        }

        if (showLogging) {
            console.log('Total Results: ');
            console.log(docs.length);
        }

        var deleteIds = [];

        // Loop through each record that has the metadata about the records with the same promotionId, customerId, and facilityId
        for (i = 0; i < docs.length; i++) {

            if (showLogging) {
                console.log('Document ' + (i + 1) + ': ');
                console.log(docs[i]);
            }

            // Loop through the ids for the individual records with the same promotionId, customerId, and facilityId
            for (j = 0; j < docs[i].docs.length; j++) {

                // Skip the first one which we'll leave as the lone survivor
                if (j !== 0) {

                    if (showLogging) {
                        console.log('Delete Unique Id: ' + docs[i].docs[j]);
                    }
                    
                    deleteIds.push(docs[i].docs[j]);
                }
            }
        }

        // Delete all the id's that we added to the array
        updateCollection.remove({ "_id": { "$in": deleteIds }}, function(err, result) {

            if (err) {
                console.log('Deleted Documents Error:');
                console.log(err);
            }

            if (result !== null && typeof result.result !== 'undefined') {
                console.log('Deleted Documents Result:');
                console.log(result.result);
            }

            db.close();
        });
    });
});