'use strict';

const Async = require('async');
const Hoek = require('hoek');
const Q = require('q');
const Request = require('request');
const _ = require('lodash');

// USAGE
// this script gives us aggregated utilization over two time frames so we can get YoY stats
// WRAP_USER - Core API User Name
// WRAP_PASS - Core API Password
// previousStartDate - start date for the previous period in YYYY-MM-DD format
// previousEndDate - end date for the previous period in YYYY-MM-DD format
// currentStartDate - start date for the current period in YYYY-MM-DD format
// currentEndDate - end date for the current period in YYYY-MM-DD format
// printHeader - 1 = print the csv header, 0 = do not print the csv header
// group - an group identifier that will be echoed in the results
// courses - comma delimited list of GNR parent ids
// node gnr-local-utilization.js previousStartDate previousEndDate currentStartDate currentEndDate printHeader group courses
// example
// WRAP_USER="test" WRAP_PASS="dontlook" node gnr-local-utilization.js 2015-12-01 2015-12-31 2016-12-01 2016-12-31 1 great-lakes 48739,29684,224

const settings = {
    wrapName: process.env.WRAP_USER || '',
    wrapPass: process.env.WRAP_PASS || '',
    wrapScope: 'http://golfnowapi.servicebus.windows.net/api/2015-01-01/Facilities/'
};

const utilDefaults = { blocked: 0, booked: 0, internet: 0, noshow: 0, paid: 0, rainout: 0, total: 0 };

const getToken = function (obj) {

    const deferred = Q.defer();

    const content = `wrap_name=${encodeURIComponent(settings.wrapName)}\n&wrap_password=${encodeURIComponent(settings.wrapPass)}\n&wrap_scope=${encodeURIComponent(settings.wrapScope)}${obj.courseId}`;
    const url = 'https://golfnowapi-sb.accesscontrol.windows.net/WRAPv0.9/';

    const options = {
        method: 'POST',
        url,
        body: content
    };

    Request(options, (err, response, body) => {

        if (err) {
            console.log('getTokenError');
            return deferred.reject(err);
        }
        const pairs = body.split('&');
        const wrapTokenArr = pairs[0].split('=');
        obj.token = decodeURIComponent(wrapTokenArr[1]);

        return deferred.resolve(obj);
    });

    return deferred.promise;
};


const getUtilization = function (obj) {

    const deferred = Q.defer();

    const url = `https://golfnowapi.servicebus.windows.net/api/2015-01-01/Facilities/${obj.courseId}/Courses/all/Statistics/TeeSheetUtilization`;

    const options = {
        method: 'GET',
        url,
        headers: {
            authorization: `WRAP access_token = "${obj.token}"`,
            accept: 'application/json'
        },
        qs: {
            StartDate: obj.startDate,
            EndDate: obj.endDate
        }
    };

    Request(options, (err, response, body) => {

        if (err) {
            console.log('getUtilizationError');
            return deferred.reject(err);
        }

        let sum = null;

        if (response.statusCode === 200 && response.headers['content-type'] === 'application/json; charset=utf-8') {

            const json = JSON.parse(body);

            sum = _.reduce(json, (result, value, key) => {

                result.blocked = (result.blocked || 0) + value.Blocked;
                result.booked = (result.booked || 0) + value.BookedSlots;
                result.internet = (result.internet || 0) + value.Internet;
                result.noshow = (result.blocked || 0) + value.NoShows;
                result.paid = (result.paid || 0) + value.Paid;
                result.rainout = (result.rainout || 0) + value.RainOuts;
                result.total = (result.total || 0) + value.TotalSlots;
                return result;
            });
        }
        else {
            sum = { blocked: 'N/A', booked: 'N/A', internet: 'N/A', noshow: 'N/A', paid: 'N/A', rainout: 'N/A', total: 'N/A' };
        }

        obj.utilization = Hoek.applyToDefaults(utilDefaults, sum || {});
        return deferred.resolve(obj);
    });

    return deferred.promise;
};

const q = Async.queue((task, callback) => {

    getToken({ courseId: task, groupId: process.argv[7] })
    .then((obj) => {

        const obj1 = _.clone(obj);
        const obj2 = _.clone(obj);

        obj1.startDate = process.argv[2];
        obj1.endDate = process.argv[3];
        obj2.startDate = process.argv[4];
        obj2.endDate = process.argv[5];

        const promises = [getUtilization(obj1), getUtilization(obj2)];
        return Q.all(promises);
    })
    .spread((previous, current) => {

        //console.log(require('util').inspect(previous, { colors: true, depth: 5 }));
        //console.log(require('util').inspect(current, { colors: true, depth: 5 }));
        console.log(`${previous.courseId},${previous.groupId},${previous.utilization.total},${previous.utilization.booked},${previous.utilization.blocked},${previous.utilization.paid},${previous.utilization.internet},${previous.utilization.noshow},${previous.utilization.rainout},${current.utilization.total},${current.utilization.booked},${current.utilization.blocked},${current.utilization.paid},${current.utilization.internet},${current.utilization.noshow},${current.utilization.rainout}`);
    })
    .fail((err) => {

        console.log(err);
        return callback(err);
    })
    .done(() => {

        return callback();
    });
}, 5);


q.drain = function () {

    // console.log('DONE');
};

const courses = process.argv[8].split(',');

if (process.argv[6].toString() === '1') {
    console.log('courseId,groupId,previous.total,previous.booked,previous.blocked,previous.paid,previous.internet,previous.noshow,previous.rainout,current.total,current.booked,current.blocked,current.paid,current.internet,current.noshow,current.rainout');
}

q.push(courses);
