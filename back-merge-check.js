'use strict';

const util = require('util');
const exec = util.promisify(require('child_process').exec);
const fs = require('fs');

let orgUrl = '**************:GolfNowEng';

const repos = [
    'app',
    'api',
    'gn-api-crm',
    'gn-api-dewey',
    'gn-api-waldo',
    'hapi-sparkpost-webhook',
    'phx-api-be',
    'phx-api-campaigns',
    'phx-api-channel',
    'phx-api-credentials',
    'phx-api-crm',
    'phx-api-g-1-cust-consumer',
    'phx-api-gateway',
    'phx-api-loyalty',
    'phx-api-manage',
    'phx-api-message-webhook',
    'phx-api-organization',
    'phx-api-payload-proxy',
    'phx-api-profile',
    'phx-api-recurring-campaigns-v2',
    'phx-api-reservation-messages',
    'phx-api-teetime-rules',
    'phx-api-teetime-search',
    'phx-api-third-party',
    'phx-api-w-5-cust-consumer',
    'phx-consumer-redis-cache-manager',
    'phx-consumer-twilio',
    'phx-fake-gn-api-21',
    'phx-hapi-plugin-customer-events',
    'phx-hapi-plugin-multi-mongo-connector',
    'phx-hapi-plugin-time-configs',
    'phx-system-user-permissions',
    'phx-ui-be',
    'phx-ui-be-5-0',
    'phx-ui-loyalty-programs',
    'phx-ui-manage',
    'worker',
];

const cloneRepo = async (orgUrl, repoName) => {

    await exec(`git clone --bare ${orgUrl}/${repoName}.git ./tmp/${repoName}`);
}

const pullRepo = async (repoName) => {

    await exec(`git --git-dir=./tmp/${repoName} pull --all`);
}

const getBranches = async (repoName) => {

    const { stdout, stderr } = await exec(`git --git-dir=./tmp/${repoName} branch -a`);

    if (stderr) {
        throw `Failed to get list of branches for repo: ${repoName}`;
    }

    const branches = stdout.split(/\r?\n/)
        .map(line => line.replace(/\*{0,1}\s{1,2}/, ''))
        .filter(line => line ? line.match(/(^master$|^develop$|^release_|^release\/|^draft_release)/) : false);

    return branches;
}

const getDiff = async (repoName, branchFrom, branchTo) => {

    const { stdout, stderr } = await exec(`git --git-dir=./tmp/${repoName} rev-list --reverse --cherry-pick --right-only ${branchTo}...${branchFrom}`);

    if (stderr) {
        throw `Failed to get diff between branches [${branchFrom}] and [${branchTo}]`;
    }

    const commits = stdout.split(/\r?\n/).filter(commit => commit);;
            if (commits.length === 0) {
                return [];
            }

    return await Promise.all(commits.map((commit) => getCommitFiles(repoName, commit))).then((values) => {
        const changes = {};
        for (const i in values) {
            if (values[i].length > 0) {
                changes[commits[i]] = values[i];
            }
        }

        return changes;
    });
}

const getCommitFiles = async (repoName, commit) => {

    const { stdout, stderr } = await exec(`git --git-dir=./tmp/${repoName} diff-tree --no-commit-id --name-only -r ${commit}`);

    if (stderr) {
        throw `Failed to get list of files in commit [${commit}] for repo: ${repoName}`;
    }

    return stdout.split(/\r?\n/).filter(file => file);
}

const getReleaseDate = (branchName) => {

    const match = branchName.match(/\d{4}\.\d{2}\.\d{2}/);

    return match ? match[0] : '';
}

const compareBranches = (branch1, branch2) => {

    if (branch1 === 'master') {
        return -1;
    }
    if (branch2 === 'master') {
        return 1;
    }
    if (branch1 === 'develop') {
        return 1;
    }
    if (branch2 === 'develop') {
        return -1;
    }

    const branch1Date = getReleaseDate(branch1);
    const branch2Date = getReleaseDate(branch2);

    if (branch2Date === branch1Date) {
        return 0;
    }

    return (branch2Date > branch1Date) ? -1 : 1;
}

(async () => {
    const args = process.argv.slice(2);

    const verbose = args.some(arg => arg === '--v');
    const keep = args.some(arg => arg === '--keep');
    const url = args.find(arg => arg.match(/^--url=/));
    if (url) {
        orgUrl = url.split('=')[1];
    }

    if (!keep) {
        fs.rmdirSync('./tmp', { recursive: true });
    }

    let backMergeRequired = false;

    for (const repoName of repos) {
        if (!keep || !fs.existsSync(`./tmp/${repoName}`)) {
            await cloneRepo(orgUrl, repoName);
        } else {
            await pullRepo(repoName);
        }
        const branches = (await getBranches(repoName)).sort(compareBranches);

        let branchFrom = branches.shift();
        let repoOk = true;
        for (const i in branches) {
            const changes = await getDiff(repoName, branchFrom, branches[i]);
            if (Object.entries(changes).length > 0) {
                repoOk = false;
                backMergeRequired = true;
                console.warn(`\u2757 ${repoName}: [${branchFrom}] -> [${branches[i]}] back-merge required!`);
                if (verbose) {
                    console.log(changes);
                }
            }
            branchFrom = branches[i];
        }
        if (repoOk) {
            console.info(`\u2705 ${repoName}`);
        }
    }

    if (!keep) {
        fs.rmdirSync('./tmp', { recursive: true });
    }

    process.exit(backMergeRequired ? 1 : 0);
})();
