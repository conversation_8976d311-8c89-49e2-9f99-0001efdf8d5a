'use strict';

const BulkMongo = require('bulk-mongo');
const { MongoClient } = require('mongodb');
const Q = require('q');

const remoteMongoURI = process.env.REMOTE_MONGO_URI || 'mongodb://192.168.99.1/gn-legacy-api';
let remoteDB = null;

const localMongoURI = process.env.LOCAL_MONGO_URI || 'mongodb://192.168.99.1:27017/gn-legacy-api';
let localDB = null;

const COLLECTION = process.env.COLLECTION || 'courses';
const REMOTE_DB_NAME = 'gn-legacy-api';
const LOCAL_DB_NAME = 'gn-legacy-api';

const QUERY = {}; // default {} to return ALL records

const remoteConnectionPromise = Q.defer();
const localConnectionPromise = Q.defer();

MongoClient.connect( remoteMongoURI )
    .then( (db) => {

        console.log( 'remote connected' );
        remoteDB = db;

        remoteConnectionPromise.resolve();
    })
    .catch( (err) => {

        remoteConnectionPromise.reject( err );
    });

MongoClient.connect( localMongoURI )
    .then( (db) => {

        console.log( 'local connected' );
        localDB = db;
        localConnectionPromise.resolve();
    })
    .catch( (err) => {

        localConnectionPromise.reject( err );
    });


Q.all( [remoteConnectionPromise.promise, localConnectionPromise.promise] )
    .then( () => {

        const Stream = remoteDB.db(REMOTE_DB_NAME).collection( COLLECTION ).find( QUERY ).stream();

        Stream.on( 'data', (chunk) => {

            console.log( chunk._id );
        });

        const factory_function = BulkMongo( localDB.db(LOCAL_DB_NAME) );
        const bulkWriter = factory_function( COLLECTION, { bulkSize: 1000 } );

        bulkWriter.on( 'inserts', (d) => {

            console.log( `d.inserts: ${d.nInserted}` );
        });

        bulkWriter.on( 'done', (doc) => {

            Stream.unpipe();

            remoteDB.close();
            localDB.close();

            console.log( 'DONE' );
            process.exit( 0 );
        });

        Stream.on( 'error', (streamErr) => {

            console.log( 'STREAM ERROR', streamErr );
            remoteDB.close();
            localDB.close();
        });

        // BOOM
        Stream.pipe( bulkWriter );
    })
    .catch( (connectError) => {

        console.log( 'Connection error:', connectError );
        process.exit( 1 );
    });
