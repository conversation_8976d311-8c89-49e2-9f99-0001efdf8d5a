'use strict';

const FastCsv = require('@fast-csv/parse');
const Async = require('async');
const Axios = require('axios');

const csvPath = process.env.CSV_PATH ;

const gateway = Axios.create({
    baseURL: process.env.GATEWAY_URL ,
    headers: {
        password: process.env.CHANNEL_PASSWORD ,
        username: process.env.CHANNEL_USERNAME
    }
});

const queue = Async.queue(async (task, done) => {

    try {
        const serviceId = await gateway.get(`courses/search/third-party/golfnow/${task.GNID}`)

        await gateway.post(`service/gn-api-waldo/mapping`,{
            tenant: 'EZTEE-PRO',
            tenantId: task.EZID,
            service: 'TL-FACILITY',
            serviceId: serviceId.data._id
        })
    }
    catch (error) {
        console.log(error.message, JSON.stringify(task))
        done()
    }

    done();
}, 20);

queue.drain(() => {

    console.log( require('util').inspect( 'All items are processed!', { colors: true, depth: 2 } ) );
});

const readCsv = () => {

    let parsed = false;

    FastCsv.parseFile(csvPath, { headers: true })
    .on('data', (data) => {

        queue.push(data);
    })
    .on('end', (end) => {

        console.log( require('util').inspect( 'Done parsing!', { colors: true, depth: 2 } ) );
    })
    .on('error', (err) => {

        console.log( require('util').inspect( err, { colors: true, depth: 2 } ) );
    });
};

// vroom vroom vroom
readCsv();
