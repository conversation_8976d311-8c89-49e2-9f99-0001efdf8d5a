const util = require('util');
const exec = util.promisify(require('child_process').exec);

const readline = require("readline");
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

const deployments = [
    'gn-api-crm-sparkpost',
    'gn-api-crm-worker',
    'gn-api-crm-consumer',
    'gn-api-crm-internal',
    'gn-api-dewey-campaigns',
    'gn-api-dewey-courses-v2',
    'gn-api-dewey-customer-v5',
    'gn-api-dewey-customerfacilities-v5',
    'gn-api-dewey-customerorganizations-v5',
    'gn-api-dewey-entity-courses-v2',
    'gn-api-dewey-entity-org-facility-groups',
    'gn-api-dewey-entity-org-groups',
    'gn-api-dewey-entity-orgs',
    'gn-api-dewey-gnc-system-users',
    'gn-api-dewey-messages-v2',
    'gn-api-dewey-reservation-journal',
    'gn-api-importer',
    'gn-api-importer-consumer',
    'gn-api-inventory-consumer',
    'gn-api-inventory-internal',
    'gn-api-waldo-internal',
    'gn-api-wonka',
    'gn-api-wonka-consumer',
    'gn-inventory-grouper',
    'gn-legacy-api-genemail',
    'gn-legacy-api-internal',
    'gn-legacy-api-low',
    'gn-legacy-api-worker',
    'gn-legacy-api-worker-system-users',
    'gn-legacy-app-internal',
    'gn-legacy-worker',
    'gn-legacy-worker-teetime-transform',
    'gn-legacy-worker-worker-low',
    'hapi-sparkpost-webhook',
    'phx-api-be',
    'phx-api-campaigns',
    'phx-api-channel',
    'phx-api-credentials',
    'phx-api-crm',
    'phx-api-g-1-cust-consumer',
    'phx-api-g-1-cust-consumer-consumer',
    'phx-api-gateway',
    'phx-api-generic-cust-consumer',
    'phx-api-gn-customer-consumer',
    'phx-api-gn-rateset-consumer',
    'phx-api-gnr-cust-consumer',
    'phx-api-loyalty',
    'phx-api-loyalty-consumer',
    'phx-api-manage',
    'phx-api-message-webhook-consumer',
    'phx-api-organization',
    'phx-api-payload-proxy',
    'phx-api-profile',
    'phx-api-recurring-campaigns-v2',
    'phx-api-recurring-campaigns-v2-consumer',
    'phx-api-reservation-journal',
    'phx-api-reservation-journal-consumer',
    'phx-api-reservation-messages',
    'phx-api-simple-webhook-g1-customer',
    'phx-api-simple-webhook-g1-membership',
    'phx-api-simple-webhook-g1-order-item',
    'phx-api-simple-webhook-g1-orders',
    'phx-api-simple-webhook-g1-payment',
    'phx-api-simple-webhook-g1-refund',
    'phx-api-simple-webhook-g1-res',
    'phx-api-simple-webhook-g1-special',
    'phx-api-teetime-rules',
    'phx-api-teetime-search',
    'phx-api-third-party',
    'phx-api-w-5-cust-consumer',
    'phx-api-w-5-webhook',
    'phx-consumer-facility-customer',
    'phx-consumer-legacy-facility',
    'phx-consumer-organization',
    'phx-consumer-twilio',
    'phx-consumer-twilio-internal',
    'phx-ui-be-5-1',
    'phx-ui-groupon',
    'phx-ui-loyalty-programs',
    'phx-ui-manage',
    'phx-ui-util-forms',
];

const restartDeployment = async (deployment) => {
    try {
        await exec(`kubectl rollout restart deploy/${deployment}`);
        console.info(`\u2705 ${deployment}`);
    } catch (e) {
        console.error(`\u2757 ${deployment}`);
        console.error(e);
        return;
    }
}

(async () => {
    const { stdout } = await exec(`kubectl config current-context`);

    const ready = await new Promise((resolve) => {
        rl.question(`Current k8s context is "${stdout.trim()}". Ready to proceed? (y/n): `, (r) => {
            resolve(r);
            rl.close();
        });
    });

    if (ready === 'y') {
        for (const deployment of deployments) {
            await restartDeployment(deployment);
        }
    }

    process.exit(0);
})();
