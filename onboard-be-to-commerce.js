const amqp = require('amqplib');
const axios = require('axios');

const config = {
    amqpUrl: '',
    queueName: 'phx-api-marketing-account-courses-temp',
    phxApiBaseUrl: 'https://stage-phx-api-be-east-1b.svc.golf',
    beURLPattern: 'https://alias-to-replace.stage-phx-ui-be.svc.golf/',
    marketingAccountCourseMaintenanceUrl: 'https://stage-phx-api-marketing-account-east-1b.svc.golf/v1/courses/maintenance'
};

const makeBeURL = (alias) => config.beURLPattern.replace('alias-to-replace', alias).replace(/\/$/, '');

console.log('Starting RabbitMQ course processor...');
console.log(`AMQP URL: ${config.amqpUrl}`);
console.log(`Queue: ${config.queueName}`);
console.log(`PHX API Base URL: ${config.phxApiBaseUrl}`);

let connection;
let channel;
let processedCoursesCount = 0;
let processedSubdomains = new Set();
let startTime = Date.now();

async function connectToRabbitMQ() {
    try {
        connection = await amqp.connect(config.amqpUrl);
        channel = await connection.createChannel();

        // Ensure queue exists
        await channel.assertQueue(config.queueName, { durable: false, autoDelete: true });

        await channel.bindQueue(config.queueName, 'gn.v1', 'phx-api-marketing-account.course.maintenance');

        // Set prefetch to 1 to process one message at a time
        await channel.prefetch(1);
        
        console.log(`Connected to RabbitMQ and listening on queue: ${config.queueName}`);
        return true;
    } catch (error) {
        console.error('Failed to connect to RabbitMQ:', error.message);
        return false;
    }
}

async function getBookingEngineSettings(courseId) {
    try {
        const url = `${config.phxApiBaseUrl}/settings/list?gnFacilityId=${courseId}&fields=subdomain,alias`;
        const response = await axios.get(url);
        return response.data;
    } catch (error) {
        console.error(`Failed to fetch booking engine settings for course ${courseId}:`, error.message);
        if (error.response) {
            console.error(`Response status: ${error.response.status}`);
            console.error(`Response data:`, error.response.data);
        }
        return null;
    }
}

async function processCourse(courseData, channel) {
    try {
        processedCoursesCount++;
        console.log('\n--- Processing Course ---');
        console.log(`Course #${processedCoursesCount} | ID: ${courseData.id ||'Unknown'} | Course Name: ${courseData.name || 'Unknown'} | GN Facility ID: ${courseData.gnFacilityId || 'Unknown'}`);

        const courseId = courseData.gnFacilityId;
        
        if (!courseId) {
            console.error('No valid course ID found in message');
            return;
        }
        
        // Get booking engine settings for this course
        const bookingEngineSettings = await getBookingEngineSettings(courseId);
        
        if (!bookingEngineSettings) {
            console.log('No booking engine settings found for this course');
            return;
        }
        
        // Handle both single object and array responses
        const settingsArray = Array.isArray(bookingEngineSettings) 
            ? bookingEngineSettings 
            : [bookingEngineSettings];
        
        console.log(`Found ${settingsArray.length} booking engine setting(s):`);

        for (const be of settingsArray) {
            const subdomain = be.subdomain;
            if (!subdomain) {
                console.log(`      ⚠️ Skipping - no subdomain`);
                continue;
            }
            
            if (processedSubdomains.has(subdomain)) {
                console.log(`      🔄 Skipping - subdomain '${subdomain}' already processed`);
                continue;
            }
            
            // Mark subdomain as processed
            processedSubdomains.add(subdomain);
            
            const url = makeBeURL(subdomain);
            const data = Buffer.from(JSON.stringify({ url }));
            channel.publish('gn.v1', 'phx-api-be.booking-engine-settings.url.changed', data);
            console.log(`      ✅ Published message for subdomain '${subdomain}'`);
        }

        // Log progress every 10 courses
        if (processedCoursesCount % 10 === 0) {
            const elapsedTime = Math.round((Date.now() - startTime) / 1000);
            const avgTimePerCourse = elapsedTime / processedCoursesCount;
            console.log(`\n📊 PROGRESS UPDATE: Processed ${processedCoursesCount} courses in ${elapsedTime}s (avg: ${avgTimePerCourse.toFixed(2)}s per course)`);
            console.log(`📊 Unique subdomains processed: ${processedSubdomains.size}`);
        }
        
    } catch (error) {
        console.error('Error processing course:', error.message);
    }
}

async function startConsumer() {
    const connected = await connectToRabbitMQ();
    if (!connected) {
        process.exit(1);
    }
    console.log(`Publishing courses to maintenance ${config.queueName}...`)
    await axios.post(config.marketingAccountCourseMaintenanceUrl);
    console.log(`Publishing courses to maintenance ${config.queueName}... DONE`)

    console.log('Starting to consume messages...');
    await channel.consume(config.queueName, async (message) => {
        if (message) {
            try {
                const courseData = JSON.parse(message.content.toString());
                
                console.log('\n=== New Message Received ===');
                await processCourse(courseData, channel);
                
                // Acknowledge the message after processing
                channel.ack(message);
            } catch (error) {
                console.error('Error processing message:', error.message);
                console.error('Message content:', message.content.toString());
                
                // Reject the message and don't requeue it to avoid infinite loops
                channel.nack(message, false, false);
            }
        }
    });
    
    console.log('Consumer started. Waiting for messages...');
    console.log('Press Ctrl+C to stop');
}

// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('\n🛑 Shutting down gracefully...');
    const elapsedTime = Math.round((Date.now() - startTime) / 1000);
    console.log(`📈 Final Stats: Processed ${processedCoursesCount} courses in ${elapsedTime}s`);
    console.log(`📈 Unique subdomains processed: ${processedSubdomains.size}`);
    if (processedCoursesCount > 0) {
        const avgTimePerCourse = elapsedTime / processedCoursesCount;
        console.log(`⏱️  Average time per course: ${avgTimePerCourse.toFixed(2)}s`);
    }
    try {
        if (channel) await channel.close();
        if (connection) await connection.close();
        console.log('Connections closed');
    } catch (error) {
        console.error('Error during shutdown:', error.message);
    }
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
    const elapsedTime = Math.round((Date.now() - startTime) / 1000);
    console.log(`📈 Final Stats: Processed ${processedCoursesCount} courses in ${elapsedTime}s`);
    console.log(`📈 Unique subdomains processed: ${processedSubdomains.size}`);
    if (processedCoursesCount > 0) {
        const avgTimePerCourse = elapsedTime / processedCoursesCount;
        console.log(`⏱️  Average time per course: ${avgTimePerCourse.toFixed(2)}s`);
    }
    try {
        if (channel) await channel.close();
        if (connection) await connection.close();
        console.log('Connections closed');
    } catch (error) {
        console.error('Error during shutdown:', error.message);
    }
    process.exit(0);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});

// Start the consumer
startConsumer().catch((error) => {
    console.error('Failed to start consumer:', error);
    process.exit(1);
});
