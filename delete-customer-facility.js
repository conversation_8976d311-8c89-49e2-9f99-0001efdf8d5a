'use strict';

// this will mark all of the customers for a particular facility as deleted
// it is then up to you if you want to do a hard delete of these customers also
// this will opt in all customers that received an error and was opted out of a particular campaign
// usage: pass in your AMQP_HOST and CUSTOMERMONGO_HOST
// usage: find the id of the bad course, in this example: 5555aaaabbbbccccddddeeee
// usage: then run the script like:
// AMQP_HOST="some rmq", CUSTOMERMONGO_HOST="some mongo" node delete-customer-facility.js 5555aaaabbbbccccddddeeee

const Amqp = require('amqplib/callback_api');
const MongoClient = require('mongodb').MongoClient;
const ObjectId = require('mongodb').ObjectID;
const Q = require('q');

let _customerDb;
let _amqpConnection;
let _channel;

const options = {
    facilityId: process.argv[2], // make sure we don't accidentally a course's db
    amqphost: process.env.AMQP_HOST || 'amqp://guest:guest@localhost',
    customermongo: process.env.CUSTOMERMONGO_HOST || 'mongodb://**************'
};

const _openAmqpConnection = function () {

    const deferred = Q.defer();

    Amqp.connect(options.amqphost, (err, conn) => {

        if (err) {
            deferred.reject(err);
        }
        else {
            console.log('AMQP SUCCESS');
            _amqpConnection = conn;
            deferred.resolve();
        }
    });

    return deferred.promise;
};


const _createChannel = function () {

    const deferred = Q.defer();

    _amqpConnection.createChannel((err, ch) => {

        if (err) {
            deferred.reject(err);
        }
        else {
            console.log('AMQP CHANNEL CREATED');
            _channel = ch;
            deferred.resolve();
        }
    });

    return deferred.promise;
};


const _openCustomerDbConnection = function () {

    const deferred = Q.defer();

    MongoClient.connect(options.customermongo, (err, db) => {

        if (err) {
            console.log(err);
            deferred.reject(err);
        }
        else {
            _customerDb = db;
            console.log('CUSTOMER DB SUCCESS');
            deferred.resolve();
        }
    });

    return deferred.promise;
};

const _getFacilityCustomerIds = function (facilityId) {

    const deferred = Q.defer();

    const collection = _customerDb.collection('customerfacilities');

    collection.find({ facilityId, deletedAt: { $exists: false } }, { facilityId: 1, customerId: 1 }).toArray((err, result) => {

        if (err) {
            console.log(err);
            deferred.reject(err);
        }
        else {
            deferred.resolve(result);
        }
    });

    return deferred.promise;
};


const _sendToQueue = function (args) {

    const deferred = Q.defer();

    for (let i = 0; i < args.length; ++i) {
        const obj = {
            method: 'DELETE',
            path: '/customer/' + args[i].customerId.toString() + '/facility/' + args[i].facilityId.toString(),
            payload: {}
        };

        _channel.sendToQueue('gn-api-customer', new Buffer(JSON.stringify(obj)), {});
    }

    deferred.resolve();

    return deferred.promise;
};

_openAmqpConnection()
.then(_createChannel)
.then(_openCustomerDbConnection)
.done(() => {

    _getFacilityCustomerIds(ObjectId(options.facilityId))
    .then(_sendToQueue)
    .delay(100)
    .done(() => {

        _customerDb.close();
        _amqpConnection.close();
        console.log('Item has been processed. Script will terminate in 5 seconds...');
        setTimeout(() => {

            process.exit();
        }, 5000);
    });
});
