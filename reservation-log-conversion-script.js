'use strict';

const MongoClient = require('mongodb').MongoClient;

const wonkaMongoUri = '';
const legacyMongoUri = '';

const wonkaCollectionName = 'reservationlogs';
const wonkaDbName = 'gn-api-wonka';
const legacyCollectionName = 'reservations';
const legacyDbName = 'gn-legacy-api';

const run = async () => {

    const wonkaClient = new MongoClient(wonkaMongoUri, { useNewUrlParser: true, useUnifiedTopology: true });
    const legacyClient = new MongoClient(legacyMongoUri, { useNewUrlParser: true, useUnifiedTopology: true });
    try {
        console.log('Connecting to legacy mongo...');
        await legacyClient.connect();
        const legacyDatabase = await legacyClient.db(legacyDbName);
        const reservations = legacyDatabase.collection(legacyCollectionName);
        console.log('Connected to legacy mongo...');

        console.log('Connecting to wonka mongo...');
        await wonkaClient.connect();
        const wonkaDatabase = await wonkaClient.db(wonkaDbName);
        const resLogs = wonkaDatabase.collection(wonkaCollectionName);
        const promotions = wonkaDatabase.collection('promotions');
        const uniques = wonkaDatabase.collection('uniques');
        console.log('Connected to wonka mongo...');

        const docs = await resLogs.find({}).batchSize(100);

        while (await docs.hasNext()) {
            const log = await docs.next();
            let master = null;

            let promo = await promotions.findOne({ code: log.code });

            // must be unique, go get it!
            if (promo === null) {
                promo = await uniques.findOne({ code: log.code });
                master = await promotions.findOne({ code: log.code.split('-')[0] });
            }

            const reservation = await reservations.findOne({ _id: log.reservationId })

            const promotionCreateDate = promo.createdAt || promo.dateFrom || master.createdAt ||  master.dateFrom;
            const promotionExpireDate = promo.dateTo || master.dateTo;

            if (reservation !== null) {
                const updatedResLog = {
                    ...log,
                    customerId: reservation.teetimes[0].userId || reservation.teetimes[0].playerId,
                    email: reservation.teetimes[0].email,
                    promotionType: promo.type || 'standard',
                    promotionCreateDate:  new Date(promotionCreateDate),
                    promotionExpireDate: new Date(promotionExpireDate),
                    promotionRedeemDate: reservation.confirmedAt,
                    discount: master?.discount || promo?.discount
                };

                console.log(log._id);

                await resLogs.findOneAndUpdate({ _id: log._id }, { $set: updatedResLog });
            }
            else {
                console.log(`Missing reservation: ${log.reservationId}`)
            }
        };
    }
    catch (err) {
        console.log(err);
    }
    finally {
        console.log('Disconnecting from mongo...');
        await wonkaClient.close();
        await legacyClient.close();
    }
};

run()
