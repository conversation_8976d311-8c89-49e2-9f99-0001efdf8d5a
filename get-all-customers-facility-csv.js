'use strict';

const Async = require('async');
const Csv = require('fast-csv');
const Fs = require('fs');
const Request = require('request');
// const Util = require('util');
const _ = require('highland');

const apiUrl = process.env.CRM_URL || 'https://gn-api-crm-east-1b.kenna.io';
const count = parseInt(process.env.CRM_COUNT) || 20;
const folder = process.env.FOLDER || 'exports';
let offset = parseInt(process.env.OFFSET) || 0;
let totalCount = parseInt(process.env.OFFSET) || 0;
// let putCount = 0;
// let failedCusts = [];

const creds = {
    id: process.env.CRM_ID,
    key: process.env.CRM_KEY,
    algorithm: 'sha256'
};

const custs = _();

//var fileStream = fs.createWriteStream(process.argv[3] || 'courses/' + process.argv[2] + '.json', { flags: 'a' });

Async.whilst(() => {

    console.log('HERE: ' + offset + ' - ' + totalCount + ' - ' + process.argv[3]);
    return offset <= totalCount;
}, (callback) => {

    const facilityIds = process.argv[2];

    const options = {
        method: 'GET',
        url: apiUrl + '/customer',
        hawk: { credentials: creds },
        json: true,
        qs: { facilityIds, count, offset }
    };

    Request(options, (err, response, body) => {

        if (err) {
            callback(err);
        }
        else {
            //console.log(Util.inspect(body, { depth: 5, colors: true }));
            _(body.items)
            .each((x) => {

                custs.write(x);
            });

            offset += count;
            if (body._pagination) {

                totalCount = body._pagination.totalCount;
            }
            callback();
        }
    });
}, (err) => {

    if (err) {
        console.log('We have an error');
        console.log(err);
        console.log('OFFSET: ' + offset);
    }
    else {
        custs.end();
        console.log('FINISHED!');
    }
});

const _transformCusts = function (cust) {

    const obj = {};
    const address = cust.addresses[0] || {};

    obj.name = cust.name || '';
    obj.email = cust.emails || '';
    obj.phone = cust.phoneNumbers || '';
    obj.address = address.streetAddress || '';
    obj.city = address.locality || '';
    obj.state = address.region || '';
    obj.country = address.country || '';
    obj.emailoptin = cust.emailOptIn || '';

    return obj;
};

custs
.map(_transformCusts)
.collect()
.map((x) => {

    Csv
    .writeToStream(Fs.createWriteStream(`${folder}/${process.argv[3]}.json`),
    x,
    { headers: true });

    return x;
})
.done((x) => {

    console.log('THISSSSSS');
});
