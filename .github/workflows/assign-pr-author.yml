name: update-pr-title-and assignee

on:
    workflow_call:

jobs:
  assign-reviewers:
    runs-on: ubuntu-latest

    steps:
      - name: Update PR Title and Assignee
        uses: actions/github-script@v7
        with:
          script: |
            // Assign the PR to the user who opened it
            await github.rest.issues.addAssignees({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.payload.pull_request.number,
              assignees: [context.payload.pull_request.user.login],
            });

            // change the PR title to include the ticket number
            // Assuming the branch name is in the format "feature/branch-name-1234"
            // and the PR title is in the format "Feature: Some feature description"
            // Extract the ticket number from the branch name
            // and prepend it to the PR title
            // Example: "Feature: Some feature description" becomes "[GNCBE-1234] Feature: Some feature description"
            // Example: "feature/branch-name-1234" becomes "1234"

            const branchName = context.payload.pull_request.head.ref;

            const validJIRABranchNamePrefixes = ['feature/gncbe', 'bugfix/gncbe', 'feature/GNCBE', 'bugfix/GNCBE'];
            if (!validJIRABranchNamePrefixes.some((prefix) => branchName.startsWith(prefix))) {
              return;
            }

            const ticketNumber = branchName.split('-').pop();

            const prTitle = context.payload.pull_request.title;

            if (parseInt(ticketNumber) && !prTitle.startsWith(`[GNCBE-${ticketNumber}]`)) {
              const newTitle = `[GNCBE-${ticketNumber}] ${prTitle.split(ticketNumber)[1]?.trim() || prTitle}`;

              await github.rest.pulls.update({
                owner: context.repo.owner,
                repo: context.repo.repo,
                pull_number: context.payload.pull_request.number,
                title: newTitle,
              });
            }
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}