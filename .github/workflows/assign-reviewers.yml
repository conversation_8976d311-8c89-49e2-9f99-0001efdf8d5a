name: assign-reviewers

on:
    workflow_call:

jobs:
    assign-reviewers:
        runs-on: ubuntu-latest

        steps:
            - name: assign reviewers
              id: assign-reviewers
              uses: actions/github-script@v7
              with:
                  script: |
                      let reviewers = [
                        'CJon<PERSON>',
                        '<PERSON><PERSON><PERSON>',
                        '<PERSON><PERSON><PERSON>',
                        'oleg-ezlinks',
                        '<PERSON><PERSON><PERSON><PERSON><PERSON>',
                        'zach<PERSON><PERSON>',
                        'polina-rude<PERSON>',
                        'bit1creative<PERSON>',
                        'gklimov-nbc',
                        '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
                        'd<PERSON><PERSON><PERSON><PERSON>kaya-uni',
                        'o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-nb<PERSON><PERSON>',
                        'julpulnaya'
                      ];

                      const author = context.payload.pull_request.user.login;

                      const reviews = await github.rest.pulls.listReviews({
                        owner: context.repo.owner,
                        repo: context.repo.repo,
                        pull_number: context.payload.pull_request.number,
                      });

                      const approvedUsers = (reviews?.data || []).filter((item) => item.state === 'APPROVED')
                      .map((item) => item.user?.login)

                      if (!!approvedUsers.length) {
                        reviewers = reviewers.filter((item) => !approvedUsers.includes(item));
                      }

                      await github.rest.pulls.requestReviewers({
                        owner: context.repo.owner,
                        repo: context.repo.repo,
                        pull_number: context.payload.pull_request.number,
                        reviewers: reviewers.filter((reviewer) => reviewer !== author),
                      });
              env:
                  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
