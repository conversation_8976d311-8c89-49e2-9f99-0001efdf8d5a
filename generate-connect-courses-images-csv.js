const aws = require('aws-sdk');
const axios = require('axios').default;
const parse = require('@fast-csv/parse');
const write = require('@fast-csv/format');
const cliProgress = require('cli-progress');

const mediaGalleryAwsRegion = process.env.AWS_REGION;
const mediaGalleryAwsPublicKey = process.env.AWS_PUBLIC_KEY;
const mediaGalleryAwsSecretKey = process.env.AWS_SECRET_KEY;
const mediaGalleryAwsBucket = process.env.AWS_BUCKET;

const beeAwsPublicKey = process.env.BEE_AWS_PUBLIC_KEY;
const beeAwsSecretKey = process.env.BEE_AWS_SECRET_KEY;
const beeAwsBucket = process.env.BEE_AWS_BUCKET;
const beeAwsRegion = process.env.BEE_AWS_REGION;

const gatewayUrl = process.env.GATEWAY_URL;
const channelPassword = process.env.CHANNEL_PASSWORD;
const channelUsername = process.env.CHANNEL_USERNAME;

const awsEntityMediaLibraryUrl = process.env.MEDIA_LIBRARY_IMAGES_AWS_BASE_URL;
const awsBeeLibraryUrl = process.env.BEE_IMAGES_AWS_BASE_URL;

const csvPath = process.env.CSV_PATH;

const apiGatewayClient = axios.create({
    baseURL: gatewayUrl,
    headers: {
        password: channelPassword,
        username: channelUsername
    }
});

const setupMediaGalleryConnection = () => {
    return new aws.S3({
        accessKeyId: mediaGalleryAwsPublicKey,
        secretAccessKey: mediaGalleryAwsSecretKey,
        region: mediaGalleryAwsRegion
    });
};

const setupBeeGalleryConnection = () => {
    return new aws.S3({
        accessKeyId: beeAwsPublicKey,
        secretAccessKey: beeAwsSecretKey,
        region: beeAwsRegion
    });
};

const getCourse = async (facilityId) => {
    return apiGatewayClient.get(`/courses/search/third-party/golfnow/${facilityId}`)
        .then((res) => res.data);
}

const readCsv = async () => {
    const courses = [];
    return new Promise(resolve => {
        parse.parseFile(csvPath, { skipLines: 1, headers: [ 'facilityId', 'gcsArea', 'market', 'accountName' ] })
            .on('data', (data) => {
                courses.push(data);
            })
            .on('end', () => {
                const count = courses.length;
                console.log( require('util').inspect( `Done parsing ${count} courses from ${csvPath}!`, { colors: true, depth: 2 } ) );

                resolve(courses)
            })
            .on('error', (err) => {
                console.log( require('util').inspect( err, { colors: true, depth: 2 } ) );
            });
    });
};

const getMediaLibraryImageUrls = async (entityId) => {
    const s3CLient = setupMediaGalleryConnection();

    const params = { 
        Bucket: mediaGalleryAwsBucket,
        Prefix: `entity-assets/${entityId}` 
    }

    const data = await s3CLient.listObjectsV2(params).promise();

    return data.Contents.filter((content) => !!content.Size).map((content) => `${awsEntityMediaLibraryUrl}/${encodeURI(content.Key)}`);
};

const getBeeLibraryImageUrls = async (alias, entityId) => {
    const s3CLient = setupBeeGalleryConnection();

    const params = { 
        Bucket: beeAwsBucket,
        Prefix: `${alias}/${entityId}` 
    }

    const data = await s3CLient.listObjectsV2(params).promise();

    return data.Contents.filter((content) => !!content.Size).map((content) => `${awsBeeLibraryUrl}/${encodeURI(content.Key)}`);
};

(async () => {
    const courses = await readCsv();

    if (courses.length === 0) {
        console.log('No courses found!');
        process.exit(0);
    }

    const buildProgressBar = new cliProgress.SingleBar(
        {
            format: 'Building Csv Rows [{bar}] {percentage}% | ETA: {eta}s | {value}/{total} | Variable: {variable}'
        },
        cliProgress.Presets.shades_classic
    );

    buildProgressBar.start(courses.length, 0);

    let failed = 0;
    const courseImages = [];
    for (const course of courses) {
        try {
            const courseData = await getCourse(course.facilityId);
    
            if (!courseData) {
                console.error(`Course with facilityId ${course.facilityId} not found!`);
                
                return;
            }
    
            const courseImageUrls = await getMediaLibraryImageUrls(courseData.id);
            const beeImageUrls = await getBeeLibraryImageUrls('images', courseData.id);
    
            [...courseImageUrls, ...beeImageUrls].forEach((imageUrl) => {
                courseImages.push([imageUrl, course.facilityId]);    
            });
        } catch (error) {
            console.error(error)
            ++failed;
        } finally {
            buildProgressBar.increment(1, { variable: course.accountName });
        }
    }

    buildProgressBar.stop();

    await write.writeToString(courseImages, { headers: ['image','fid'] })
        .then((data) => console.log(data));

    if (failed) {
        console.warn(`Failed to build rows for ${failed} courses!`);
    }

    process.exit(0);
})();