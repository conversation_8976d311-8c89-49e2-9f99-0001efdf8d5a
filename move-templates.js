const axios = require('axios').default;

const QA_URL = "https://qa-phx-api-messaging-east-1b.svc.golf";
const STAGE_URL = "https://stage-phx-api-messaging-east-1b.svc.golf";
const PROD_URL = "https://prod-phx-api-messaging-east-1b.svc.golf";
const JWT_SECRET = "secret";



function createApiClient(baseURL) {
    return axios.create({
        baseURL,
        headers: {
            'user-agent': 'phx-dev-tools',
            Authorization: `Bearer ${createTokenWithPayload()}`
        }
    });
}



async function getTemplate(apiClient, templateType) {
    try {
        const response = await apiClient.get('/email-templates/', {
            params: {
                'type': 'transactional',
                'returnDefault': true
            }
        });

        const templates = response.data;

        // Find template with matching messageType
        const matchingTemplate = templates.find(template =>
            template.messageType === templateType
        );

        return matchingTemplate || null;
    } catch (error) {
        console.error(`Error fetching templates: ${error.message}`);
        throw error;
    }
}

async function createTemplate(apiClient, templateData) {
    try {
        const response = await apiClient.post('/email-templates/', templateData);
        return response.data;
    } catch (error) {
        console.error(`Error creating template: ${error.message}`);
        throw error;
    }
}

async function updateTemplate(apiClient, templateId, templateData) {
    try {
        const response = await apiClient.put(`/email-templates/${templateId}`, templateData);
        return response.data;
    } catch (error) {
        console.error(`Error updating template: ${error.message}`);
        throw error;
    }
}

async function addTemplate(fromEnvUrl, toEnvUrl, templateType) {
    try {
        console.log(`Moving template '${templateType}' from ${fromEnvUrl} to ${toEnvUrl}`);

        // Create API clients for both environments
        const fromApiClient = createApiClient(fromEnvUrl);
        const toApiClient = createApiClient(toEnvUrl);

        // Get template from source environment
        console.log(`Fetching template '${templateType}' from source environment...`);
        const sourceTemplate = await getTemplate(fromApiClient, templateType);

        if (!sourceTemplate) {
            throw new Error(`Template with messageType '${templateType}' not found in source environment`);
        }

        console.log(`Found template: ${sourceTemplate.name || sourceTemplate.messageType}`);

        // Check if template exists in target environment
        console.log(`Checking if template exists in target environment...`);
        const targetTemplate = await getTemplate(toApiClient, templateType);

        // Prepare template data (remove fields that shouldn't be copied)
        const templateData = { ...sourceTemplate };
        delete templateData._id;
        delete templateData.createdAt;
        delete templateData.updatedAt;
        delete templateData.__v;

        let result;
        if (targetTemplate) {
            // Template exists, update it
            console.log(`Template exists in target environment. Updating...`);
            result = await updateTemplate(toApiClient, targetTemplate._id, templateData);
            console.log(`Template updated successfully`);
        } else {
            // Template doesn't exist, create it
            console.log(`Template doesn't exist in target environment. Creating...`);
            result = await createTemplate(toApiClient, templateData);
            console.log(`Template created successfully`);
        }

        return {
            success: true,
            action: targetTemplate ? 'updated' : 'created',
            templateType,
            sourceTemplate,
            result
        };

    } catch (error) {
        console.error(`Error in addTemplate: ${error.message}`);
        return {
            success: false,
            error: error.message,
            templateType
        };
    }
}

