// simply take the csv of tee times and our facility mapping
// and dump the data into rabbit

// usage: node rabbit_stream_stdout.js gn-api-inventory-error >> ./errors/gn-api-inventory-errors.json
// usage: node rabbit_stream_stdout.js gn-api-customer-error >> ./errors/gn-api-customer-errors.json

'use strict';

const Fs = require('fs');
const Q = require('q');
const JSONStream = require('JSONStream');

// passed around?
const locals = {};

const connectToRabbitMQStream = function () {

    const deferred = Q.defer();

    // create the context
    const context = require('rabbit.js').createContext(process.env.AMQP_URI || 'amqp://dev:66f8783cb5cb4045b8ce66f79177b819@localhost:5672');

    // create a socket
    locals.pull = context.socket('PULL', { noCreate: true, prefetch: 5000 });

    // connect!
    locals.pull.connect(process.argv[2], () => deferred.resolve());

    // listen for the close event (exit the process when done)
    locals.pull.on('close', () => process.exit(0));

    return deferred.promise;
};


const consumeQueue = function () {

    // defer
    const deferred = Q.defer();

    // we want to pipe it back out (stdout)
    // process.stdout.setEncoding('utf8');
    // const fileStream = process.stdout;
    const fileStream = Fs.createWriteStream(process.argv[3] || process.argv[2] + '.json');

    locals.pull
    .pipe(JSONStream.parse(true))
    .pipe(JSONStream.stringify(false))
    .pipe(fileStream)
    .on('end', () => deferred.resolve());

    // return the promise
    return deferred.promise;
};

// kick it off!
Q(connectToRabbitMQStream())
.then(() => Q.fcall(consumeQueue))
.fail((err) => {

    console.log(err);
    process.exit(1);
})
.fin(() => locals.pub.close());
