// usage: cat golfnow-rateset-feed-error.txt | node rabbit_stream_stdin.js

'use strict';
const HL = require('highland');
const R = require('ramda');

const gfIds = [];

const run = async function () {

    console.log( 'starting ...' );

    process.stdin.setEncoding( 'utf8' );
    await new Promise( (resolve) => {

        HL( process.stdin )
            .split()
            .consume( function (err, x, push, next) {

                if (err) {
                    push( err );
                    next();
                }
                else if (x === HL.nil) {
                    push( null, x );
                }
                else {

                    if (!x) {
                        push( null, x );
                        next();
                        return;
                    }

                    const data = JSON.parse( x );
                    
                    if (R.path( ['payload', 'GFStatus'] )( data ) === 'false') {
                        
                        const gfId = R.path( ['payload', 'GolfFacilityID'] )( data );
                        if (R.indexOf( gfId )( gfIds ) === -1) {

                            gfIds.push( gfId );
                            console.log( R.path( ['payload', 'GFName'] )( data ) );
                        }
                    }

                    push( null, x );
                    next();
                }
            })
            .done( resolve );
    });

    console.log( 'complete!', gfIds.length );
};

// kick it off!
run();
