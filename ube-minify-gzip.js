'use strict';

const Async = require('async');
const Fs = require('fs');
const Minimist = require('minimist');
const Uglify = require('uglify-js');
const Zlib = require('zlib');
const _ = require('lodash');

// run this script using the following structure
// node ube-minify-gzip.js -v [version] [path to uncompressed js]
// node ube-minify-gzip.js -v 9.9 /Users/<USER>/gn-ube/JS
const args = Minimist(process.argv.slice(2));

const minifyAndCompress = Async.queue((task, callback) => {

    // get the file contents
    const fileString = Fs.readFileSync(`${args._[0]}/${task}`, 'utf8');
    // minify the contents
    const minified = Uglify.minify(fileString, { compress: true, mangle: true, ie8: true });
    // gzip the stuff
    const compressed = Zlib.gzipSync(minified.code);
    // write the file
    Fs.writeFile(`${args.v}/${task}`, compressed);
    console.log(`WROTE ${args.v}/${task}`);
    return callback();
}, 1);

// make our directory
Fs.mkdir(args.v.toString());
// get the files, filter out anything but .js files
const files = _.filter(Fs.readdirSync(args._[0]), (val) => _.endsWith(val, '.js'));
// push in the list of the files
minifyAndCompress.push(files);
// drain function
minifyAndCompress.drain = function () {

    console.log('WE ARE DONE');
};
