'use strict';

const { exec, spawn } = require('child_process');

const useEnv = function (env) {

    return new Promise( (resolve, reject) => {

        exec( `kubectl config use-context ${env}`, {}, (error, stdout, stderr) => {

            if (error) {
                console.error( error );
                reject( false );
                return;
            }

            if (/^Switched\sto\scontext/.test( stdout ) !== true) {
                console.error( stdout );
                reject( false );
                return;
            }

            resolve( true );
        });
    });
};

const portForward = function (env, options) {

    const envOptions = options[env];

    const namespace = envOptions.ns;
    let service = envOptions.svc;
    if (envOptions.repl !== false) {
        service += `-${envOptions.repl}`;
    }

    let cmd = `kubectl port-forward svc/${service} -n ${namespace} ${options.localPort}:${envOptions.remotePort}`;

    if (options.address) {
        cmd += ` --address ${options.address}`;
    }

    console.log( cmd );

    spawn( cmd, { stdio: 'inherit', shell: true } );
};

module.exports = { useEnv, portForward };
