{"name": "kc-port-forward", "version": "1.0.0", "description": "a cli tool for expediting kubectl port-forward commands for kubernetes services", "main": "cli.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "bin": {"pofo": "./cli.js"}, "dependencies": {"@hapi/eslint-config-hapi": "^12.3.0", "@hapi/eslint-plugin-hapi": "^4.3.4", "commander": "^2.20.0", "eslint": "^6.7.2", "inquirer": "^6.2.2", "ramda": "^0.26.1"}}