# POFO
### a cli tool for expediting kubectl port-forward commands for mongo dbs in Kenna

## Install
```
npm install
npm link
```

## Usage
`pofo use stage mongo`

`pofo use stage mongo crm`

`pofo use prod mongo`

`pofo use prod redis`

`pofo use prod es messages`

`pofo use prod mongo legacy-api`

---

```
pofo --help

Usage: pofo [options] [command]

a cli tool for expediting kubectl port-forward commands for kubernetes services

Options:
  -V, --version              output the version number
  -h, --help                 output usage information

Commands:
  use [options] <env> <type> [svc]  port-forward a kubernetes service
```

---

```
pofo use --help

Usage: use [options] <env> <type> [svc]

port-forward a kubernetes service

Options:
  -l, --localport <n>   the local port number
  -r, --remoteport <n>  the remote port number
  -n, --namespace <ns>  the namespace of the service
  -i, --instance <n>    the replica-set instance number
  -a, --address <n>     the binding IP to listen on (comma-separated)
  -h, --help            output usage information
```
