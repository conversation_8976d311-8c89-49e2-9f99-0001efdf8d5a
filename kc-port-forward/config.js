'use strict';

const envs = [
    {
        name: 'prod',
        env: 'gke_golfnow-b2b_us-east1_b2b-001-us-east1'
    },
    {
        name: 'qa',
        env: 'gke_golfnow-b2b-qa_us-east1-b_golfnow-b2b-qa-001-us-east1'
    },
    {
        name: 'stage',
        env: 'gke_golfnow-staging_us-east1-b_golfnow-staging-us-east1-b'
    },
    {
        name: 'qa',
        env: 'gke_golfnow-b2b-qa_us-east1-b_golfnow-b2b-qa-001-us-east1'
    
    }
];

const es = [
    {
        name: 'messages',
        localPort: 9700,
        prod: {
            ns: 'default',
            remotePort: 9200,
            repl: false,
            svc: 'messages-es'
        },
        qa: {
            ns: 'default',
            remotePort: 9200,
            repl: false,
            svc: 'messages-es'
        },
        stage: {
            ns: 'default',
            remotePort: 9200,
            repl: false,
            svc: 'messages-es'
        }
    },
    {
        name: 'crm',
        localPort: 9700,
        prod: {
            ns: 'default',
            remotePort: 9200,
            repl: false,
            svc: 'gn-api-crm-55-prd-es'
        },
        qa: {
            ns: 'default',
            remotePort: 9200,
            repl: false,
            svc: 'gn-api-crm-55-es'
        },
        stage: {
            ns: 'default',
            remotePort: 9200,
            repl: false,
            svc: 'gn-api-crm-55-es'
        },
        qa: {
            ns: 'default',
            remotePort: 9200,
            repl: false,
            svc: 'gn-api-crm-55-es'
        }
    },
    {
        name: 'org',
        localPort: 9700,
        prod: {
            ns: 'default',
            remotePort: 9200,
            repl: false,
            svc: 'gn-api-org-es'
        },
        qa: {
            ns: 'default',
            remotePort: 9200,
            repl: false,
            svc: 'gn-api-org-es'
        },
        stage: {
            ns: 'default',
            remotePort: 9200,
            repl: false,
            svc: 'gn-api-org-es'
        }
    },
    {
        name: 'res-journal',
        localPort: 9700,
        prod: {
            ns: 'default',
            remotePort: 9200,
            repl: false,
            svc: 'gn-api-res-es'
        },
        qa: {
            ns: 'default',
            remotePort: 9200,
            repl: false,
            svc: 'gn-api-res-es'
        },
        stage: {
            ns: 'default',
            remotePort: 9200,
            repl: false,
            svc: 'gn-api-res-es'
        }
    }
];

const mongo = [
    {
        name: 'crm',
        localPort: 55300,
        prod: {
            ns: 'data',
            remotePort: 27017,
            repl: 1,
            svc: 'gn-api-crm-mgo'
        },
        qa: {
            ns: 'data',
            remotePort: 27017,
            repl: 1,
            svc: 'stage-gn-api-customer-mgo'
        },
        stage: {
            ns: 'data',
            remotePort: 27017,
            repl: 1,
            svc: 'stage-gn-api-customer-mgo'
        }
    },
    {
        name: 'inventory',
        localPort: 55302,
        prod: {
            ns: 'data',
            remotePort: 27017,
            repl: 1,
            svc: 'gn-api-inventory-mgo'
        },
        qa: {
            ns: 'data',
            remotePort: 27017,
            repl: 1,
            svc: 'stage-gn-api-inventory-mgo'
        },
        stage: {
            ns: 'data',
            remotePort: 27017,
            repl: 1,
            svc: 'stage-gn-api-inventory-mgo'
        }
    },
    {
        name: 'legacy-api',
        localPort: 55301,
        prod: {
            ns: 'data',
            remotePort: 27017,
            repl: 1,
            svc: 'gn-legacy-api-mgo'
        },
        qa: {
            ns: 'data',
            remotePort: 27017,
            repl: 1,
            svc: 'gn-legacy-api-mgo'
        },
        stage: {
            ns: 'data',
            remotePort: 27017,
            repl: 1,
            svc: 'stage-gn-legacy-api-mgo'
        },
        qa: {
            ns: 'data',
            remotePort: 27017,
            repl: 1,
            svc: 'gn-legacy-api-mgo'
        }
    },
    {
        name: 'organization',
        localPort: 55303,
        prod: {
            ns: 'data',
            remotePort: 27017,
            repl: false,
            svc: 'phx-api-organization-mgo'
        },
        qa: {
            ns: 'default',
            remotePort: 3593,
            repl: false,
            svc: 'qa-phx-api-org-db'
        },
        stage: {
            ns: 'default',
            remotePort: 3593,
            repl: false,
            svc: 'qa-phx-api-org-db'
        }
    },
    {
        name: 'recurring',
        localPort: 55304,
        prod: {
            ns: 'data',
            remotePort: 27017,
            repl: 1,
            svc: 'phx-api-recurring-campaigns-mgo'
        },
        qa: {
            ns: 'default',
            remotePort: 14018,
            repl: 1,
            svc: 'qa-recur-campaign-mgo'
        },
        stage: {
            ns: 'default',
            remotePort: 14018,
            repl: 1,
            svc: 'qa-recur-campaign-mgo'
        }
    },
    {
        name: 'wonka',
        localPort: 55305,
        prod: {
            ns: 'data',
            remotePort: 27017,
            repl: 1,
            svc: 'phx-api-promo-mgo'
        },
        qa: {
            ns: 'data',
            remotePort: 27017,
            repl: 1,
            svc: 'stage-gn-api-promo-mgo'
        },
        stage: {
            ns: 'data',
            remotePort: 27017,
            repl: 1,
            svc: 'stage-gn-api-promo-mgo'
        }
    },
    {
        name: 'channel',
        localPort: 55306,
        prod: {
            ns: 'data',
            remotePort: 27017,
            repl: 1,
            svc: 'phx-api-auth-mgo'
        },
        qa: {
            ns: 'default',
            remotePort: 8477,
            repl: 1,
            svc: 'qa-phx-api-auth-mongo'
        },
        stage: {
            ns: 'default',
            remotePort: 8477,
            repl: 1,
            svc: 'qa-phx-api-auth-mongo'
        },
        qa: {
            ns: 'data',
            remotePort: 27017,
            repl: 1,
            svc: 'phx-api-auth-mgo'
        }
    },
    {
        name: 'loyalty',
        localPort: 55307,
        prod: {
            ns: 'data',
            remotePort: 27017,
            repl: 1,
            svc: 'phx-api-loyalty-mgo'
        },
        qa: {
            ns: 'default',
            remotePort: 34917,
            repl: false,
            svc: 'phx-api-loyalty-db'
        },
        stage: {
            ns: 'default',
            remotePort: 34917,
            repl: false,
            svc: 'phx-api-loyalty-db'
        }
    },
    {
        name: 'credentials',
        localPort: 55308,
        prod: {
            ns: 'data',
            remotePort: 27017,
            repl: 1,
            svc: 'phx-api-auth-mgo'
        },
        qa: {
            ns: 'default',
            remotePort: 8477,
            repl: 1,
            svc: 'qa-phx-api-auth-mongo'
        },
        stage: {
            ns: 'default',
            remotePort: 8477,
            repl: 1,
            svc: 'qa-phx-api-auth-mongo'
        }
    },
    {
        name: 'waldo',
        localPort: 55309,
        prod: {
            ns: 'data',
            remotePort: 27017,
            repl: 1,
            svc: 'phx-api-mappings-mgo'
        },
        qa: {
            ns: 'data',
            remotePort: 27017,
            repl: 1,
            svc: 'stage-gn-api-mappings-mgo'
        },
        stage: {
            ns: 'data',
            remotePort: 27017,
            repl: 1,
            svc: 'stage-gn-api-mappings-mgo'
        }
    },
    {
        name: 'res-journal',
        localPort: 55310,
        prod: {
            ns: 'data',
            remotePort: 27017,
            repl: 1,
            svc: 'phx-api-reservation-journal-mgo'
        },
        qa: {
            ns: 'data',
            remotePort: 27017,
            repl: 1,
            svc: 'stage-phx-api-reservation-journal-mgo'
        },
        stage: {
            ns: 'data',
            remotePort: 27017,
            repl: 1,
            svc: 'stage-phx-api-reservation-journal-mgo'
        }
    },
    {
        name: 'rules',
        localPort: 55311,
        prod: {
            ns: 'data',
            remotePort: 27017,
            repl: 1,
            svc: 'phx-api-teetime-rules-mgo'
        },
        qa: {
            ns: 'data',
            remotePort: 27017,
            repl: 1,
            svc: 'stage-phx-api-teetime-rules-mgo'
        },
        stage: {
            ns: 'data',
            remotePort: 27017,
            repl: 1,
            svc: 'stage-phx-api-teetime-rules-mgo'
        }
    },
    {
        name: 'rollup',
        localPort: 55312,
        prod: {
            ns: 'data',
            remotePort: 27017,
            repl: 1,
            svc: 'phx-api-customer-rollup-mgo'
        },
        qa: {
            ns: 'data',
            remotePort: 27017,
            repl: 1,
            svc: 'stage-phx-api-customer-rollup-mgo'
        },
        stage: {
            ns: 'data',
            remotePort: 27017,
            repl: 1,
            svc: 'stage-phx-api-customer-rollup-mgo'
        }
    },
    {
        name: 'quisten',
        localPort: 55313,
        prod: {
            ns: 'data',
            remotePort: 27017,
            repl: false,
            svc: 'quisten-mgo'
        }
    }
];

const redis = [
    {
        name: 'res-journal',
        localPort: 37541,
        prod: {
            ns: 'default',
            remotePort: 37541,
            repl: false,
            svc: 'phx-api-resjou-redis'
        },
        qa: {
            ns: 'default',
            remotePort: 37541,
            repl: false,
            svc: 'qa-phx-api-resjou-redis'
        },
        stage: {
            ns: 'default',
            remotePort: 37541,
            repl: false,
            svc: 'qa-phx-api-resjou-redis'
        }
    },
    {
        name: 'cust-rollup',
        localPort: 37542,
        prod: {
            ns: 'default',
            remotePort: 6379,
            repl: false,
            svc: 'phx-api-customer-rollup-redis'
        },
        qa: {
            ns: 'default',
            remotePort: 6379,
            repl: false,
            svc: 'phx-api-customer-rollup-redis'
        },
        stage: {
            ns: 'default',
            remotePort: 6379,
            repl: false,
            svc: 'phx-api-customer-rollup-redis'
        }
    },
    {
        name: 'campaigns-api',
        localPort: 37543,
        prod: {
            ns: 'default',
            remotePort: 6379,
            repl: false,
            svc: 'phx-api-campaigns-redis'
        },
        qa: {
            ns: 'default',
            remotePort: 6379,
            repl: false,
            svc: 'stage-phx-api-campaigns-redis'
        },
        stage: {
            ns: 'default',
            remotePort: 6379,
            repl: false,
            svc: 'stage-phx-api-campaigns-redis'
        }
    }
];

module.exports = { envs, es, mongo, redis };
