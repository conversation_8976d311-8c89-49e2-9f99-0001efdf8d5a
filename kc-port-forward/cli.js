#!/usr/bin/env node

'use strict';

const C = require('commander');
const Config = require('./config');
const Kubectl = require('./kubectl');
const Package = require('./package.json');
const { prompt: Prompt } = require('inquirer');
const R = require('ramda');

const run = async function (env, type, svc, cmd) {

    if (env !== 'prod' && env !== 'qa' && env !== 'stage') {
        console.error( 'Invalid environment. Must be either "prod", "qa", or "stage".' );
        process.exit();
    }

    if (type !== 'mongo' && type !== 'redis' && type !== 'es') {
        console.error( 'Invalid service type. Must be either "mongo", "redis", or "es".' );
        process.exit();
    }

    const switched = await Kubectl.useEnv( R.prop( 'env' )( R.find( R.propEq( 'name', env ) )( Config.envs ) ) );

    if (switched !== true) {
        process.exit();
    }

    let svcName = svc;
    if (!svc) {
        const answers = await Prompt([{
            type: 'list',
            name: 'svc',
            message: 'Select a service:',
            choices: R.reduce( (reduced, item) => {

                return (R.has( env )( item ) === true) ? R.append( item.name )( reduced ) : reduced;
            }, [] )( Config[type] )
        }]);

        svcName = answers.svc;
    }

    const options = R.clone( R.find( R.propEq( 'name', svcName ) )( Config[type] ) );

    if (options === undefined || R.has( env )( options ) !== true) {
        console.error( `unknown service: ${svcName}` );
        process.exit();
    }

    if (cmd.address) {
        options.address = cmd.address;
    }
    else {
        options.address = '0.0.0.0';
    }

    if (cmd.localport) {
        options.localPort = cmd.localport;
    }

    if (cmd.remoteport) {
        options[env].remotePort = cmd.remoteport;
    }

    if (cmd.namespace) {
        options[env].ns = cmd.namespace;
    }

    if (cmd.instance) {
        options[env].repl = cmd.instance;
    }

    Kubectl.portForward( env, options );
};

C.version( Package.version ).description( Package.description );

C.command( 'use <env> <type> [svc]' )
    .option( '-a, --address <n>', 'addresses to listen on', R.compose( R.trim, R.toLower ) )
    .option( '-l, --localport <n>', 'the local port number', parseInt )
    .option( '-r, --remoteport <n>', 'the remote port number', parseInt )
    .option( '-n, --namespace <ns>', 'the namespace of the service', R.compose( R.trim, R.toLower ) )
    .option( '-i, --instance <n>', 'the replica-set instance number', parseInt )
    .description( 'port-forward a kubernetes service' )
    .action( run );

C.parse( process.argv );
